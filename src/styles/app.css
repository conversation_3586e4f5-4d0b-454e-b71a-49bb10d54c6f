page {
    /* ==================== 颜色系统 ==================== */
    /* 主题色 */
    --primary-50: #fff9ec;
    --primary-100: #fff1d3;
    --primary-200: #ffdfa5;
    --primary-300: #ffc66d;
    --primary-400: #ffa232;
    --primary-500: #ff860a;
    --primary-600: #ff6d00;
    --primary-700: #cc4e02;
    --primary-800: #a13d0b;
    --primary-900: #82340c;
    --primary-950: #461804;

    --primary: var(--primary-600);

    /* 文本颜色 */
    --text-red: #f52c37;
    --text-green: #22c55e;
    --text-blue: #3b82f6;
    --text-yellow: #eab308;
    --text-purple: #8b5cf6;

    --text-inverse: #feffff;
    --text-base: #212529;
    --text-secondary: #515359;
    --text-info: #8e8e93;
    --text-grey: #a1a1aa;
    --text-disable: #d4d4d8;
    --border-color: #f0f0f1;

    /* 背景颜色 */
    --bg-page: #f4f5f9;
    --bg-tag: #f3f5f7;
    --bg-card: #feffff;
    --bg-search: #f1f2f7;
    --bg-input: #f2f2f7;
    --bg-mask: rgba(0, 0, 0, 0.5);

    /* 功能色背景 */
    --bg-primary-light: rgba(255, 109, 0, 0.1);
    --bg-success-light: rgba(34, 197, 94, 0.1);
    --bg-warning-light: rgba(234, 179, 8, 0.1);
    --bg-danger-light: rgba(220, 38, 38, 0.1);
    --bg-info-light: rgba(59, 130, 246, 0.1);

    /* ==================== 尺寸和间距 ==================== */
    /* 圆角尺寸 */
    --radius-sm: 8rpx;
    --radius: 16rpx;
    --radius-lg: 24rpx;
    --radius-xl: 32rpx;
    --radius-xxl: 40rpx;

    /* 间距尺寸 */
    --spacing-4: 8rpx;
    --spacing-6: 12rpx;
    --spacing-8: 16rpx;
    --spacing-10: 20rpx;
    --spacing-12: 24rpx;
    --spacing-14: 28rpx;
    --spacing-16: 32rpx;
    --spacing-18: 36rpx;
    --spacing-20: 40rpx;
    --spacing-24: 48rpx;
    --spacing-28: 56rpx;
    --spacing-32: 64rpx;
    --spacing-36: 72rpx;
    --spacing-40: 80rpx;

    /* 字体大小 */
    --font-size-xs: 24rpx;
    --font-size-sm: 26rpx;
    --font-size-base: 28rpx;
    --font-size-md: 30rpx;
    --font-size-lg: 32rpx;
    --font-size-xl: 36rpx;
    --font-size-xxl: 40rpx;

    /* 字体粗细 */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 700;

    /* 行高 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.4;
    --line-height-loose: 1.75;
}

.safe-area-inset-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

view {
    box-sizing: border-box;
}

image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* 使用gpu加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

button::after {
    border: none;
}

.page,
uni-page-wrapper,
page {
    color: var(--text-base);
    background: var(--bg-page);
    font-size: 28rpx;
}

.container {
    background: var(--bg-page);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.wrap {
    flex: 1;
    background-color: #fff;
}

.content {
    margin: 20rpx;
    flex: 1;
}

/* 背景色工具类 */
.bg-page {
    background-color: var(--bg-page);
}

.bg-tag {
    background-color: var(--bg-tag);
}

.bg-card {
    background-color: var(--bg-card);
}

.bg-search {
    background-color: var(--bg-search);
}

.bg-mask {
    background-color: var(--bg-mask);
}

.bg-primary-light {
    background-color: var(--bg-primary-light);
}

.bg-success-light {
    background-color: var(--bg-success-light);
}

.bg-warning-light {
    background-color: var(--bg-warning-light);
}

.bg-danger-light {
    background-color: var(--bg-danger-light);
}

.bg-info-light {
    background-color: var(--bg-info-light);
}

/* 文本色工具类 */
.text-primary {
    color: var(--primary);
}

.text-red {
    color: var(--text-red);
}

.text-green {
    color: var(--text-green);
}

.text-blue {
    color: var(--text-blue);
}

.text-yellow {
    color: var(--text-yellow);
}

.text-purple {
    color: var(--text-purple);
}

.text-base {
    color: var(--text-base);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-info {
    color: var(--text-info);
}

.text-grey {
    color: var(--text-grey);
}

.text-disable {
    color: var(--text-disable);
}

.text-inverse {
    color: var(--text-inverse);
}

/* 兼容旧版类名 */
.primary {
    color: var(--primary);
}

.color-main {
    color: var(--text-base);
}

.color-secondary {
    color: var(--text-secondary);
}

.color-info {
    color: var(--text-info);
}

.color-grey {
    color: var(--text-grey);
}

.color-placeholder {
    color: var(--text-grey);
}

/* 字体工具类 */
.font-xs {
    font-size: var(--font-size-xs);
}

.font-sm {
    font-size: var(--font-size-sm);
}

.font-base {
    font-size: var(--font-size-base);
}

.font-md {
    font-size: var(--font-size-md);
}

.font-lg {
    font-size: var(--font-size-lg);
}

.font-xl {
    font-size: var(--font-size-xl);
}

.font-xxl {
    font-size: var(--font-size-xxl);
}

.font-light {
    font-weight: var(--font-weight-light);
}

.font-regular {
    font-weight: var(--font-weight-regular);
}

.font-medium {
    font-weight: var(--font-weight-medium);
}

.font-bold {
    font-weight: var(--font-weight-bold);
}

/* 文本工具类 */
.text-line-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-line-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-line-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

/* 分割线 */
.divider {
    height: 1rpx;
    background-color: var(--border-color);
    width: 100%;
    margin: var(--spacing-8) 0;
}

.divider-dashed {
    border-top: 1rpx dashed var(--border-color);
    background-color: transparent;
}

.divider-vertical {
    width: 1rpx;
    height: 100%;
    margin: 0 var(--spacing-8);
}

.tag-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    gap: 12rpx;
}

.urgent-tag {
    border: 3rpx solid var(--text-red);
    color: var(--text-red);
    font-size: 24rpx;
    font-weight: 500;
    padding: 0 6rpx;
    line-height: 1.3;
    border-radius: 8rpx;
    flex-shrink: 0;
}

/* 标签系统 */
.tag {
    display: inline-block;
    padding: var(--spacing-6) var(--spacing-10);
    background-color: var(--bg-tag);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    margin-right: var(--spacing-4);
    margin-bottom: var(--spacing-4);
    line-height: 1.4;
}

.tag-sm {
    padding: 4rpx 12rpx;
    font-size: 26rpx;
}

.tag-lg {
    padding: var(--spacing-8) var(--spacing-12);
    font-size: var(--font-size-base);
}

/* 标签类型变体 */
.tag-primary {
    background-color: var(--bg-primary-light);
    color: var(--primary);
}

.tag-success {
    background-color: var(--bg-success-light);
    color: var(--text-green);
}

.tag-warning {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
}

.tag-danger {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
}

.tag-info {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
}

/* 标签边框变体 */
.tag-outlined {
    background-color: transparent;
    border: 1rpx solid var(--border-color);
}

.tag-primary-outlined {
    background-color: transparent;
    border: 1rpx solid var(--primary);
    color: var(--primary);
}

.tag-success-outlined {
    background-color: transparent;
    border: 1rpx solid var(--text-green);
    color: var(--text-green);
}

.tag-warning-outlined {
    background-color: transparent;
    border: 1rpx solid var(--text-yellow);
    color: var(--text-yellow);
}

.tag-danger-outlined {
    background-color: transparent;
    border: 1rpx solid var(--text-red);
    color: var(--text-red);
}

/* 标签容器 */
.tag-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: var(--spacing-8);
}

/* 带阴影的卡片 */
.box-shadow {
    box-shadow: 0 4rpx 12rpx rgba(195, 195, 210, 0.1);
}

.search-bar {
    height: 72rpx;
    padding: 0;
    background-color: var(--bg-search);
    border-radius: 36rpx;
    gap: 12rpx;
}

/* 添加缺失的CSS类 */
.bg-transparent {
    background-color: transparent;
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.from-primary-50 {
    background: linear-gradient(to bottom, var(--primary-50), transparent);
}

/* 补充圆角尺寸 */
.rounded-sm {
    border-radius: var(--radius-sm);
}

.rounded {
    border-radius: var(--radius);
}

.rounded-xl {
    border-radius: var(--radius-xl);
}

.rounded-xxl {
    border-radius: var(--radius-xxl);
}

/* 补充阴影类 */
.shadow-sm {
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.shadow {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.shadow-lg {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}
