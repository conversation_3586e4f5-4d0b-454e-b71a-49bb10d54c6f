// Simple test for job store functionality
// This is a basic test to validate the job store works correctly

import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from 'pinia'
import { useJobStore } from '@/stores/job'

describe('Job Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  test('should initialize with default values', () => {
    const jobStore = useJobStore()
    
    expect(jobStore.currentRole).toBe('jobseeker')
    expect(jobStore.isJobseeker).toBe(true)
    expect(jobStore.isRecruiter).toBe(false)
  })

  test('should switch roles correctly', () => {
    const jobStore = useJobStore()
    
    // Switch to recruiter
    jobStore.switchRole('recruiter')
    expect(jobStore.currentRole).toBe('recruiter')
    expect(jobStore.isJobseeker).toBe(false)
    expect(jobStore.isRecruiter).toBe(true)
    
    // Switch back to jobseeker
    jobStore.switchRole('jobseeker')
    expect(jobStore.currentRole).toBe('jobseeker')
    expect(jobStore.isJobseeker).toBe(true)
    expect(jobStore.isRecruiter).toBe(false)
  })

  test('should update filters correctly', () => {
    const jobStore = useJobStore()
    
    // Test jobseeker filters
    jobStore.updateJobseekerFilters('experience', ['1-3年'])
    expect(jobStore.jobseekerFilters.experience).toEqual(['1-3年'])
    
    // Test recruiter filters
    jobStore.switchRole('recruiter')
    jobStore.updateRecruiterFilters('status', ['面试中'])
    expect(jobStore.recruiterFilters.status).toEqual(['面试中'])
  })

  test('should reset filters correctly', () => {
    const jobStore = useJobStore()
    
    // Modify filters first
    jobStore.updateJobseekerFilters('experience', ['1-3年'])
    jobStore.updateJobseekerFilters('salary', ['15-25K'])
    
    // Reset filters
    jobStore.resetFilters()
    
    expect(jobStore.jobseekerFilters.experience).toEqual(['全部'])
    expect(jobStore.jobseekerFilters.salary).toEqual(['全部'])
  })

  test('should manage intention jobs correctly', () => {
    const jobStore = useJobStore()
    
    // Add new intention job
    jobStore.addIntentionJob('测试工程师')
    const newJob = jobStore.intentionJobs.find(job => job.name === '测试工程师')
    expect(newJob).toBeDefined()
    expect(newJob.active).toBe(false)
    
    // Select intention job
    jobStore.selectIntentionJob(newJob.id)
    expect(newJob.active).toBe(true)
    
    // Remove intention job
    const initialCount = jobStore.intentionJobs.length
    jobStore.removeIntentionJob(newJob.id)
    expect(jobStore.intentionJobs.length).toBe(initialCount - 1)
  })
})
