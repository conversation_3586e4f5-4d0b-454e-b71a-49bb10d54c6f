// Test for TalentItem component functionality
// This validates the enhanced TalentItem component works correctly

import { mount } from '@vue/test-utils'
import TalentItem from '@/components/job/TalentItem.vue'

describe('TalentItem Component', () => {
  const mockTalent = {
    id: 1,
    name: '张三',
    gender: '男',
    age: 28,
    avatar: 'https://example.com/avatar.jpg',
    jobTitle: '前端开发工程师',
    experience: '3年经验',
    workExperience: '3年经验',
    education: '本科',
    location: '广州天河区',
    expectedSalary: '15-25K',
    isOnline: true,
    lastActivity: '刚刚',
    skills: ['Vue.js', 'React', 'TypeScript', 'Node.js', '微信小程序'],
    status: 'active'
  }

  test('should render talent information correctly', () => {
    const wrapper = mount(TalentItem, {
      props: {
        talent: mockTalent
      }
    })

    // Check if name is displayed
    expect(wrapper.text()).toContain('张三')
    
    // Check if job title is displayed
    expect(wrapper.text()).toContain('前端开发工程师')
    
    // Check if age and gender are displayed
    expect(wrapper.text()).toContain('28岁')
    expect(wrapper.text()).toContain('男')
    
    // Check if experience is displayed
    expect(wrapper.text()).toContain('3年经验')
    
    // Check if location is displayed
    expect(wrapper.text()).toContain('广州天河区')
    
    // Check if expected salary is displayed
    expect(wrapper.text()).toContain('15-25K')
  })

  test('should display skills correctly', () => {
    const wrapper = mount(TalentItem, {
      props: {
        talent: mockTalent
      }
    })

    // Check if skills are displayed (first 5 skills)
    expect(wrapper.text()).toContain('Vue.js')
    expect(wrapper.text()).toContain('React')
    expect(wrapper.text()).toContain('TypeScript')
    expect(wrapper.text()).toContain('Node.js')
    expect(wrapper.text()).toContain('微信小程序')
  })

  test('should show online status when talent is online', () => {
    const wrapper = mount(TalentItem, {
      props: {
        talent: mockTalent
      }
    })

    expect(wrapper.text()).toContain('在线')
  })

  test('should emit click event when talent item is clicked', async () => {
    const wrapper = mount(TalentItem, {
      props: {
        talent: mockTalent
      }
    })

    await wrapper.find('.talent-item').trigger('tap')
    
    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')[0]).toEqual([mockTalent])
  })

  test('should emit contact event when contact button is clicked', async () => {
    const wrapper = mount(TalentItem, {
      props: {
        talent: mockTalent
      }
    })

    await wrapper.find('.primary-btn').trigger('tap')
    
    expect(wrapper.emitted('contact')).toBeTruthy()
    expect(wrapper.emitted('contact')[0]).toEqual([mockTalent])
  })

  test('should display correct status tag for different statuses', () => {
    const interviewTalent = { ...mockTalent, status: 'interview' }
    const wrapper = mount(TalentItem, {
      props: {
        talent: interviewTalent
      }
    })

    expect(wrapper.text()).toContain('面试中')
  })

  test('should handle missing optional fields gracefully', () => {
    const minimalTalent = {
      id: 2,
      name: '李四',
      jobTitle: '后端工程师',
      skills: []
    }

    const wrapper = mount(TalentItem, {
      props: {
        talent: minimalTalent
      }
    })

    // Should still render without errors
    expect(wrapper.text()).toContain('李四')
    expect(wrapper.text()).toContain('后端工程师')
    
    // Should show default values
    expect(wrapper.text()).toContain('25岁') // default age
    expect(wrapper.text()).toContain('男') // default gender
  })
})
