<template>
  <view class="mine-page" :class="{ 'is-login': isLoggedIn }">
    <!-- 背景渐变 -->
    <view class="page-background"></view>

    <uni-nav-bar
      :border="false"
      :fixed="true"
      :background-color="'transparent'"
      status-bar="true"
      :placeholder="true"
    />

    <!-- 未登录状态 -->
    <view v-if="!isLoggedIn" class="not-logged-in">
      <view class="content">
        <image src="/static/images/logo.png" class="logo" mode="aspectFit" />
        <view class="title">您好，请登录</view>
        <view class="subtitle">登录后解锁更多精彩内容</view>
        <button class="login-btn" @click="handleLogin">微信一键登录</button>
      </view>
    </view>

    <!-- 已登录状态 -->
    <view v-else class="logged-in">
      <!-- 用户信息卡片 -->
      <view class="user-card" @click="navigateTo('/pages/mine/profile')">
        <view class="user-info">
          <image :src="userAvatar" class="avatar" mode="aspectFill" />
          <view class="details">
            <view class="name-section">
              <text class="name">{{ displayName }}</text>
              <view v-if="user?.isVip" class="vip-tag">VIP</view>
            </view>
            <view class="intro">{{ user?.intro || '这个人很神秘，什么也没留下~' }}</view>
          </view>
        </view>
        <view class="arrow">
          <tui-icon name="arrowright" :size="20" color="#BDBDBD"></tui-icon>
        </view>
      </view>

      <!-- 用户数据统计 -->
      <view class="stats-card">
        <view class="stat-item">
          <text class="value">{{ user?.posts || 0 }}</text>
          <text class="label">我的发布</text>
        </view>
        <view class="stat-item">
          <text class="value">{{ user?.followers || 0 }}</text>
          <text class="label">收藏</text>
        </view>
        <view class="stat-item">
          <text class="value">{{ user?.following || 0 }}</text>
          <text class="label">足迹</text>
        </view>
      </view>

      <!-- 核心功能区 -->
      <view class="section-card">
        <view class="section-title">核心服务</view>
        <view class="grid-menu">
          <view
            v-for="item in coreFeatures"
            :key="item.title"
            class="grid-item"
            @click="navigateTo(item.path)"
          >
            <view class="grid-icon-wrapper" :style="{ backgroundColor: item.bgColor }">
              <tui-icon :name="item.iconName" :size="26" :color="item.iconColor"></tui-icon>
            </view>
            <text class="grid-label">{{ item.title }}</text>
          </view>
        </view>
      </view>

      <!-- 通用功能区 -->
      <view class="section-card">
        <view class="section-title">通用设置</view>
        <view class="list-menu">
          <view
            v-for="item in otherFeatures"
            :key="item.title"
            class="list-item"
            @click="handleMenuItemClick(item)"
          >
            <view class="list-item-left">
              <view class="list-icon-wrapper" :style="{ backgroundColor: item.bgColor }">
                <tui-icon :name="item.iconName" :size="20" :color="item.iconColor"></tui-icon>
              </view>
              <text class="list-label">{{ item.title }}</text>
            </view>
            <view class="list-item-right">
              <tui-icon name="arrowright" :size="18" color="#d1d1d1"></tui-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const isLoggedIn = computed(() => userStore.isLoggedIn)
const user = computed(() => userStore.user)
const displayName = computed(() => userStore.displayName)
const userAvatar = computed(() => userStore.userAvatar)

// 核心功能菜单
const coreFeatures = [
  {
    title: '我的发布',
    iconName: 'edit',
    bgColor: '#EBF5FF',
    iconColor: '#3B82F6',
    path: '/pages/post/mine',
  },
  {
    title: '我的收藏',
    iconName: 'star',
    bgColor: '#FFFBEB',
    iconColor: '#F59E0B',
    path: '/pages/mine/collections',
  },
  {
    title: '我的订单',
    iconName: 'order',
    bgColor: '#ECFDF5',
    iconColor: '#10B981',
    path: '/pages/order/list',
  },
  {
    title: '商户入驻',
    iconName: 'shop',
    bgColor: '#EFF6FF',
    iconColor: '#6366F1',
    path: '/pages/merchant/apply',
  },
]

// 其他功能菜单
const otherFeatures = [
  {
    title: '实名认证',
    iconName: 'shield',
    bgColor: '#EFF6FF',
    iconColor: '#6366F1',
    path: '/pages/auth/real-name',
    action: 'navigate',
  },
  {
    title: '联系客服',
    iconName: 'service',
    bgColor: '#F0F9FF',
    iconColor: '#0EA5E9',
    path: '',
    action: 'contact',
  },
  {
    title: '关于我们',
    iconName: 'app',
    bgColor: '#F3F4F6',
    iconColor: '#6B7280',
    path: '/pages/about/us',
    action: 'navigate',
  },
  {
    title: '退出登录',
    iconName: 'exit',
    bgColor: '#FEF2F2',
    iconColor: '#EF4444',
    path: '',
    action: 'logout',
  },
]

// 模拟登录
const handleLogin = () => {
  userStore.mockLogin()
}

// 菜单项点击处理
const handleMenuItemClick = (item: (typeof otherFeatures)[0]) => {
  if (item.action === 'navigate') {
    navigateTo(item.path)
  } else if (item.action === 'logout') {
    uni.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          userStore.clearUserInfo()
        }
      },
    })
  } else if (item.action === 'contact') {
    uni.makePhoneCall({
      phoneNumber: '18888888888', // 在这里替换为实际的客服电话
    })
  }
}

// 统一的页面跳转
const navigateTo = (path: string) => {
  if (!path) return
  uni.navigateTo({ url: path })
}
</script>

<style lang="scss" scoped>
.mine-page {
  position: relative;
  min-height: 100vh;
  padding-bottom: 48rpx;
  background-color: #f8f9fa;
  overflow-x: hidden;

  &.is-login {
    .page-background {
      height: 420rpx;
    }
  }
}

.page-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  background: linear-gradient(175deg, #dbeafe 5%, #eef2ff 40%, var(--bg-page) 80%);
  transition: height 0.4s ease-out;
  z-index: 0;
}

// 未登录
.not-logged-in {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - var(--window-top));
  padding: 0 60rpx;
  text-align: center;

  .logo {
    width: 180rpx;
    height: 180rpx;
    border-radius: 50%;
    margin-bottom: 40rpx;
    border: 6rpx solid #ffffff;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  .title {
    font-size: 40rpx;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 16rpx;
  }

  .subtitle {
    font-size: 28rpx;
    color: var(--gray-500);
    margin-bottom: 80rpx;
  }

  .login-btn {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    background: linear-gradient(135deg, var(--primary), var(--primary-400));
    border-radius: 100rpx;
    border: none;
    box-shadow: 0 8rpx 20rpx rgba(var(--primary-rgb), 0.2);
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
      opacity: 0.9;
    }
  }
}

// 已登录
.logged-in {
  position: relative;
  z-index: 1;
  padding: 0 32rpx;
}

.user-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;

  .user-info {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      border: 4rpx solid #ffffff;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    }

    .details {
      .name-section {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }
      .name {
        font-size: 40rpx;
        font-weight: 600;
        color: var(--gray-900);
      }
      .vip-tag {
        background: linear-gradient(135deg, #fde047, #facc15);
        color: #854d0e;
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
        font-size: 20rpx;
        font-weight: bold;
      }
      .intro {
        font-size: 24rpx;
        color: var(--gray-500);
        margin-top: 8rpx;
        max-width: 400rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.stats-card {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: $radius-lg;
  box-shadow: 0 8rpx 40rpx rgba(var(--primary-rgb), 0.06);
  margin-bottom: 32rpx;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    .value {
      font-size: 36rpx;
      font-weight: 700;
      color: var(--gray-900);
    }
    .label {
      font-size: 24rpx;
      color: var(--gray-500);
    }
  }
}

.section-card {
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: $radius-lg;
  box-shadow: 0 8rpx 40rpx rgba(var(--primary-rgb), 0.06);
  margin-bottom: 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    margin-bottom: 32rpx;
  }
}

.grid-menu {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;

  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;

    .grid-icon-wrapper {
      width: 100rpx;
      height: 100rpx;
      border-radius: $radius-lg;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: transform 0.2s ease;
    }

    &:active .grid-icon-wrapper {
      transform: scale(0.92);
    }

    .grid-label {
      font-size: 24rpx;
      color: var(--gray-600);
    }
  }
}

.list-menu {
  display: flex;
  flex-direction: column;

  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    transition: background-color 0.15s ease;
    margin: 0 -32rpx;
    padding: 24rpx 32rpx;

    &:active {
      background-color: #f9fafb;
    }
  }
  .list-item-left {
    display: flex;
    align-items: center;
    gap: 24rpx;
  }

  .list-icon-wrapper {
    width: 64rpx;
    height: 64rpx;
    border-radius: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .list-label {
    font-size: 28rpx;
    color: var(--gray-800);
  }
}
</style>
