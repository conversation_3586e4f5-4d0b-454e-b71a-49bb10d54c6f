<template>
  <view class="privacy-page">
    <view class="privacy-content">
      <!-- 个人信息隐私 -->
      <view class="privacy-section">
        <view class="section-title">个人信息隐私</view>
        <view class="privacy-list">
          <view class="privacy-item">
            <view class="item-left">
              <text class="i-solar-phone-linear text-20rpx text-green-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">手机号可见性</text>
                <text class="item-desc">设置谁可以看到您的手机号</text>
              </view>
            </view>
            <view class="item-right">
              <text class="item-value">仅自己可见</text>
              <text class="i-solar-arrow-right-linear text-16rpx text-gray-400"></text>
            </view>
          </view>
          
          <view class="privacy-item">
            <view class="item-left">
              <text class="i-solar-user-id-linear text-20rpx text-blue-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">真实姓名</text>
                <text class="item-desc">是否在个人资料中显示真实姓名</text>
              </view>
            </view>
            <view class="item-right">
              <tui-switch v-model="showRealName"></tui-switch>
            </view>
          </view>
        </view>
      </view>

      <!-- 活动隐私 -->
      <view class="privacy-section">
        <view class="section-title">活动隐私</view>
        <view class="privacy-list">
          <view class="privacy-item">
            <view class="item-left">
              <text class="i-solar-eye-linear text-20rpx text-purple-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">在线状态</text>
                <text class="item-desc">允许其他用户看到您的在线状态</text>
              </view>
            </view>
            <view class="item-right">
              <tui-switch v-model="showOnlineStatus"></tui-switch>
            </view>
          </view>
          
          <view class="privacy-item">
            <view class="item-left">
              <text class="i-solar-history-linear text-20rpx text-orange-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">浏览记录</text>
                <text class="item-desc">允许记录您的浏览历史</text>
              </view>
            </view>
            <view class="item-right">
              <tui-switch v-model="allowBrowsingHistory"></tui-switch>
            </view>
          </view>
          
          <view class="privacy-item">
            <view class="item-left">
              <text class="i-solar-map-point-linear text-20rpx text-red-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">位置信息</text>
                <text class="item-desc">允许应用获取您的位置信息</text>
              </view>
            </view>
            <view class="item-right">
              <tui-switch v-model="allowLocation"></tui-switch>
            </view>
          </view>
        </view>
      </view>

      <!-- 推荐隐私 -->
      <view class="privacy-section">
        <view class="section-title">推荐与匹配</view>
        <view class="privacy-list">
          <view class="privacy-item">
            <view class="item-left">
              <text class="i-solar-like-linear text-20rpx text-pink-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">个性化推荐</text>
                <text class="item-desc">基于您的行为为您推荐相关内容</text>
              </view>
            </view>
            <view class="item-right">
              <tui-switch v-model="personalizedRecommend"></tui-switch>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 数据管理 -->
      <view class="privacy-section">
        <view class="section-title">数据管理</view>
        <view class="privacy-list">
          <view class="privacy-item" @click="exportData">
            <view class="item-left">
              <text class="i-solar-download-linear text-20rpx text-indigo-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">导出个人数据</text>
                <text class="item-desc">下载您在本应用的所有数据</text>
              </view>
            </view>
            <view class="item-right">
              <text class="i-solar-arrow-right-linear text-16rpx text-gray-400"></text>
            </view>
          </view>
          
          <view class="privacy-item" @click="deleteAccount">
            <view class="item-left">
              <text class="i-solar-trash-bin-trash-linear text-20rpx text-red-500 mr-3"></text>
              <view class="item-info">
                <text class="item-label">删除账号</text>
                <text class="item-desc">永久删除您的账号和所有数据</text>
              </view>
            </view>
            <view class="item-right">
              <text class="i-solar-arrow-right-linear text-16rpx text-red-400"></text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 隐私设置状态
const showRealName = ref(false)
const showOnlineStatus = ref(true)
const allowBrowsingHistory = ref(true)
const allowLocation = ref(false)
const personalizedRecommend = ref(true)

// 导出个人数据
const exportData = () => {
  uni.showModal({
    title: '导出个人数据',
    content: '我们将为您准备数据包，处理完成后会通过邮件发送给您',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '已提交导出申请',
          icon: 'success'
        })
      }
    }
  })
}

// 删除账号
const deleteAccount = () => {
  uni.showModal({
    title: '删除账号',
    content: '此操作不可逆，将永久删除您的账号和所有数据。确定要继续吗？',
    confirmColor: '#F44336',
    success: (res) => {
      if (res.confirm) {
        uni.showModal({
          title: '最后确认',
          content: '您确定要删除账号吗？此操作无法撤销！',
          confirmColor: '#F44336',
          success: (res2) => {
            if (res2.confirm) {
              uni.showToast({
                title: '账号删除申请已提交',
                icon: 'success'
              })
            }
          }
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.privacy-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.privacy-content {
  padding: 32rpx;
}

.privacy-section {
  margin-bottom: 48rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--text-base);
    margin-bottom: 24rpx;
    padding-left: 8rpx;
  }
}

.privacy-list {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.privacy-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f8f9fa;
  }
  
  .item-left {
    display: flex;
    align-items: flex-start;
    flex: 1;
    
    .item-info {
      .item-label {
        font-size: 28rpx;
        color: var(--text-base);
        margin-bottom: 8rpx;
        display: block;
      }
      
      .item-desc {
        font-size: 24rpx;
        color: var(--text-secondary);
        line-height: 1.4;
      }
    }
  }
  
  .item-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
    
    .item-value {
      font-size: 26rpx;
      color: var(--text-secondary);
      text-align: right;
    }
  }
}
</style> 