<template>
  <view class="settings-page">
    <CustomNavBar title="账号设置" />
    
    <view class="settings-content">
      <!-- 账号信息 -->
      <view class="settings-section">
        <view class="section-title">账号信息</view>
        <view class="settings-list">
          <view class="setting-item" @click="editProfile">
            <view class="item-left">
              <tui-icon name="user" :size="40" color="#2196F3"></tui-icon>
              <text class="item-label">个人资料</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{ userStore.user?.name || '未设置' }}</text>
              <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
            </view>
          </view>
          
          <view class="setting-item" @click="editPhone">
            <view class="item-left">
              <tui-icon name="phone" :size="40" color="#4CAF50"></tui-icon>
              <text class="item-label">手机号码</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{ userStore.user?.phone || '未绑定' }}</text>
              <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
            </view>
          </view>
          
          <view class="setting-item" @click="changePassword">
            <view class="item-left">
              <tui-icon name="lock" :size="40" color="#FF9800"></tui-icon>
              <text class="item-label">修改密码</text>
            </view>
            <view class="item-right">
              <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="settings-section">
        <view class="section-title">应用设置</view>
        <view class="settings-list">
          <view class="setting-item">
            <view class="item-left">
              <tui-icon name="notification" :size="40" color="#FF4081"></tui-icon>
              <text class="item-label">推送通知</text>
            </view>
            <view class="item-right">
              <tui-switch v-model="notificationEnabled" @change="handleNotificationChange"></tui-switch>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-left">
              <tui-icon name="sound" :size="40" color="#9C27B0"></tui-icon>
              <text class="item-label">声音提醒</text>
            </view>
            <view class="item-right">
              <tui-switch v-model="soundEnabled" @change="handleSoundChange"></tui-switch>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-left">
              <tui-icon name="vibrate" :size="40" color="#607D8B"></tui-icon>
              <text class="item-label">震动反馈</text>
            </view>
            <view class="item-right">
              <tui-switch v-model="vibrateEnabled" @change="handleVibrateChange"></tui-switch>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他设置 -->
      <view class="settings-section">
        <view class="section-title">其他设置</view>
        <view class="settings-list">
          <view class="setting-item" @click="clearCache">
            <view class="item-left">
              <tui-icon name="delete" :size="40" color="#F44336"></tui-icon>
              <text class="item-label">清理缓存</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{ cacheSize }}</text>
              <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
            </view>
          </view>
          
          <view class="setting-item" @click="checkUpdate">
            <view class="item-left">
              <tui-icon name="refresh" :size="40" color="#2196F3"></tui-icon>
              <text class="item-label">检查更新</text>
            </view>
            <view class="item-right">
              <text class="item-value">v1.0.0</text>
              <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'
import CustomNavBar from '@/components/CustomNavBar.vue'

const userStore = useUserStore()

// 设置状态
const notificationEnabled = ref(true)
const soundEnabled = ref(true)
const vibrateEnabled = ref(true)
const cacheSize = ref('12.5MB')

// 编辑个人资料
const editProfile = () => {
  uni.navigateTo({
    url: '/pages/mine/profile'
  })
}

// 编辑手机号
const editPhone = () => {
  uni.showToast({
    title: '手机号修改功能开发中',
    icon: 'none'
  })
}

// 修改密码
const changePassword = () => {
  uni.showToast({
    title: '密码修改功能开发中',
    icon: 'none'
  })
}

// 处理通知设置变化
const handleNotificationChange = (e: any) => {
  console.log('通知设置:', e.detail.value)
}

// 处理声音设置变化
const handleSoundChange = (e: any) => {
  console.log('声音设置:', e.detail.value)
}

// 处理震动设置变化
const handleVibrateChange = (e: any) => {
  console.log('震动设置:', e.detail.value)
}

// 清理缓存
const clearCache = () => {
  uni.showModal({
    title: '清理缓存',
    content: '确定要清理应用缓存吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({ title: '清理中...' })
        setTimeout(() => {
          uni.hideLoading()
          cacheSize.value = '0MB'
          uni.showToast({
            title: '缓存清理完成',
            icon: 'success'
          })
        }, 2000)
      }
    }
  })
}

// 检查更新
const checkUpdate = () => {
  uni.showLoading({ title: '检查中...' })
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '已是最新版本',
      icon: 'success'
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
.settings-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.settings-content {
  padding: 0 32rpx 40rpx;
}

.settings-section {
  margin-bottom: 48rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 24rpx;
    padding-left: 8rpx;
  }
}

.settings-list {
  background: var(--bg-card);
  border-radius: 16rpx;
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: var(--bg-hover);
  }
  
  .item-left {
    display: flex;
    align-items: center;
    gap: 24rpx;
    
    .item-label {
      font-size: 28rpx;
      color: var(--text-primary);
    }
  }
  
  .item-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
    
    .item-value {
      font-size: 26rpx;
      color: var(--text-secondary);
    }
  }
}
</style> 