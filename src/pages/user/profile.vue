<template>
  <view class="profile-page">
    <CustomNavBar title="个人资料" />
    
    <view class="profile-content">
      <!-- 头像区域 -->
      <view class="avatar-section">
        <view class="avatar-container" @click="changeAvatar">
          <image 
            :src="userInfo.avatar || '/static/images/default-avatar.png'"
            mode="aspectFill"
            class="user-avatar"
          />
          <view class="avatar-mask">
            <text class="i-carbon-camera avatar-icon"></text>
            <text class="avatar-text">更换头像</text>
          </view>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="info-section">
        <view class="info-card">
                     <view class="info-item" @click="editName">
             <text class="info-label">姓名</text>
             <view class="info-value-wrapper">
               <text class="info-value">{{ userInfo?.name || '未设置' }}</text>
               <text class="i-carbon-chevron-right info-arrow"></text>
             </view>
           </view>
           
           <view class="info-item" @click="editJobTitle">
             <text class="info-label">职业</text>
             <view class="info-value-wrapper">
               <text class="info-value">{{ userInfo?.jobTitle || '未设置' }}</text>
               <text class="i-carbon-chevron-right info-arrow"></text>
             </view>
           </view>
           
           <view class="info-item" @click="editCompany">
             <text class="info-label">公司</text>
             <view class="info-value-wrapper">
               <text class="info-value">{{ userInfo?.company || '未设置' }}</text>
               <text class="i-carbon-chevron-right info-arrow"></text>
             </view>
           </view>
          
          <view class="info-item" @click="editLocation">
            <text class="info-label">所在地</text>
            <view class="info-value-wrapper">
              <text class="info-value">{{ userInfo.location || '未设置' }}</text>
              <text class="i-carbon-chevron-right info-arrow"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="contact-section">
        <view class="section-title">联系方式</view>
        <view class="info-card">
          <view class="info-item" @click="editPhone">
            <text class="info-label">手机号</text>
            <view class="info-value-wrapper">
              <text class="info-value">{{ formatPhone(userInfo.phone) }}</text>
              <text class="i-carbon-chevron-right info-arrow"></text>
            </view>
          </view>
          
          <view class="info-item" @click="editEmail">
            <text class="info-label">邮箱</text>
            <view class="info-value-wrapper">
              <text class="info-value">{{ userInfo.email || '未绑定' }}</text>
              <text class="i-carbon-chevron-right info-arrow"></text>
            </view>
          </view>
        </view>
      </view>

             <!-- 个人简介 -->
       <view class="bio-section">
         <view class="section-title">个人简介</view>
         <view class="bio-card" @click="editIntro">
           <text class="bio-text">{{ userInfo?.intro || '介绍一下自己吧...' }}</text>
           <text class="i-carbon-edit bio-edit-icon"></text>
         </view>
       </view>
     </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import CustomNavBar from '@/components/CustomNavBar.vue'

const userStore = useUserStore()
const userInfo = computed(() => userStore.getUser)

// 弹窗状态 - 已移除不需要的状态

// 方法
const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 这里应该上传图片到服务器
      // 暂时直接使用本地路径
      userStore.updateUserInfo({ avatar: res.tempFilePaths[0] })
      uni.showToast({ title: '头像更新成功', icon: 'success' })
    }
  })
}

const editName = () => {
  uni.navigateTo({
    url: '/pages/mine/edit-field?field=name&title=姓名&value=' + encodeURIComponent(userInfo.value?.name || '')
  })
}

const editJobTitle = () => {
  uni.navigateTo({
    url: '/pages/mine/edit-field?field=jobTitle&title=职业&value=' + encodeURIComponent(userInfo.value?.jobTitle || '')
  })
}

const editCompany = () => {
  uni.navigateTo({
    url: '/pages/mine/edit-field?field=company&title=公司&value=' + encodeURIComponent(userInfo.value?.company || '')
  })
}

const editLocation = () => {
  uni.navigateTo({
    url: '/pages/mine/edit-field?field=location&title=所在地&value=' + encodeURIComponent(userInfo.value.location || '')
  })
}

const editPhone = () => {
  uni.navigateTo({
    url: '/pages/mine/edit-field?field=phone&title=手机号&value=' + encodeURIComponent(userInfo.value.phone || '')
  })
}

const editEmail = () => {
  uni.navigateTo({
    url: '/pages/mine/edit-field?field=email&title=邮箱&value=' + encodeURIComponent(userInfo.value.email || '')
  })
}

const editIntro = () => {
  uni.navigateTo({
    url: '/pages/mine/edit-field?field=intro&title=个人简介&value=' + encodeURIComponent(userInfo.value?.intro || '')
  })
}

const formatPhone = (phone: string) => {
  if (!phone) return '未绑定'
  if (phone.length === 11) {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
  }
  return phone
}
</script>

<style lang="scss" scoped>
.profile-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.profile-content {
  padding: 32rpx;
}

// 头像区域
.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 48rpx;
}

.avatar-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 6rpx solid #fff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 100%;
  height: 100%;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  .avatar-icon {
    font-size: 32rpx;
    color: #fff;
    margin-bottom: 8rpx;
  }

  .avatar-text {
    font-size: 20rpx;
    color: #fff;
  }
}

.avatar-container:active .avatar-mask {
  opacity: 1;
}

// 信息区域
.info-section,
.contact-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

.info-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: var(--bg-hover);
  }
}

.info-label {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.info-value-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.info-value {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.info-arrow {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

// 个人简介
.bio-section {
  margin-bottom: 32rpx;
}

.bio-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: relative;
  min-height: 120rpx;
  display: flex;
  align-items: flex-start;
  transition: background-color 0.2s ease;

  &:active {
    background-color: var(--bg-hover);
  }
}

.bio-text {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

.bio-edit-icon {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-left: 16rpx;
}

// 性别选择器
.gender-picker {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: 32rpx;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.picker-cancel,
.picker-confirm {
  font-size: 28rpx;
  color: var(--primary);
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.gender-options {
  padding: 16rpx 0;
}

.gender-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  transition: background-color 0.2s ease;

  &:active {
    background-color: var(--bg-hover);
  }

  &.active {
    background-color: var(--primary-50);
  }
}

.gender-text {
  font-size: 30rpx;
  color: var(--text-primary);
}

.gender-check {
  font-size: 24rpx;
  color: var(--primary);
}
</style> 