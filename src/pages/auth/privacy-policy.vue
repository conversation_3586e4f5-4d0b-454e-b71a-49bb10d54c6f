<template>
  <view class="privacy-policy-page">
    <view class="content">
      <view class="title">本地宝平台隐私政策</view>
      <view class="update-info"
        >更新日期：2024年1月1日 | 生效日期：2024年1月1日</view
      >

      <view class="paragraph"
        >欢迎您使用本地宝平台！我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。</view
      >

      <view class="paragraph"
        >请在使用我们的产品（或服务）前，仔细阅读并了解本《隐私政策》。如果您不同意本政策的任何内容，您应立即停止使用我们的服务。当您开始使用本地宝平台服务时，即表示您已经同意我们按照本政策收集、使用、储存和分享您的相关信息。</view
      >

      <view class="subtitle">一、我们收集的信息</view>
      <view class="paragraph"
        >为了向您提供服务，我们会根据您使用的具体服务内容，收集以下类型的个人信息：</view
      >

      <view class="section-title">1.1 账户注册信息</view>
      <view class="list-item"
        >手机号码：用于账户注册、登录验证、安全校验</view
      >
      <view class="list-item">微信OpenID：微信登录时获取，用于身份识别</view>
      <view class="list-item">用户名、昵称：用于账户识别和平台内展示</view>
      <view class="list-item"
        >头像：用户个人形象展示，可通过微信授权获取</view
      >

      <view class="section-title">1.2 身份认证信息</view>
      <view class="list-item">真实姓名：用于实名认证，保障交易安全</view>
      <view class="list-item">身份证号码：用于身份验证，防范虚假信息</view>
      <view class="list-item"
        >身份证照片：部分服务需要上传身份证件照片进行人工审核</view
      >

      <view class="section-title">1.3 求职招聘相关信息</view>
      <view class="list-item"
        >简历信息：包括教育背景、工作经历、技能特长、期望薪资等</view
      >
      <view class="list-item"
        >职位发布信息：岗位需求、薪资待遇、工作地点、联系方式等</view
      >
      <view class="list-item"
        >企业认证信息：营业执照、企业地址、法人信息等</view
      >
      <view class="list-item">面试沟通记录：平台内的沟通消息和通话记录</view>

      <view class="section-title">1.4 房产服务相关信息</view>
      <view class="list-item"
        >房源信息：房屋地址、面积、价格、装修情况、房屋照片等</view
      >
      <view class="list-item"
        >购房/租房需求：预算范围、区域偏好、房型要求等</view
      >
      <view class="list-item">看房记录：看房时间、地点、评价等</view>
      <view class="list-item">房产证明：房产证、土地证等产权证明文件</view>

      <view class="section-title">1.5 零工服务相关信息</view>
      <view class="list-item"
        >技能认证：技能证书、工作经验证明、服务评价等</view
      >
      <view class="list-item"
        >服务记录：服务时间、地点、服务内容、客户评价等</view
      >
      <view class="list-item">收入信息：服务费用、结算记录等</view>

      <view class="section-title">1.6 交友相亲相关信息</view>
      <view class="list-item"
        >个人资料：年龄、性别、身高、体重、学历、职业等</view
      >
      <view class="list-item">择偶条件：年龄范围、学历要求、职业偏好等</view>
      <view class="list-item">个人照片：用于个人形象展示</view>
      <view class="list-item">聊天记录：平台内的交友沟通记录</view>

      <view class="section-title">1.7 位置信息</view>
      <view class="list-item"
        >精确位置信息：用于附近职位推荐、房源查找、服务匹配等</view
      >
      <view class="list-item"
        >常用地址：家庭地址、工作地址等，用于服务推荐优化</view
      >

      <view class="section-title">1.8 设备与技术信息</view>
      <view class="list-item"
        >设备标识符：设备型号、操作系统版本、应用版本等</view
      >
      <view class="list-item">网络信息：IP地址、网络类型、运营商信息等</view>
      <view class="list-item">日志信息：访问时间、访问页面、操作记录等</view>
      <view class="list-item">崩溃日志：用于应用稳定性优化</view>

      <view class="section-title">1.9 支付信息</view>
      <view class="list-item">支付记录：交易时间、金额、支付方式等</view>
      <view class="list-item">发票信息：开票抬头、税号等（如需开票）</view>

      <view class="subtitle">二、信息使用目的</view>
      <view class="paragraph"
        >我们严格遵守法律法规的规定及与用户的约定，将收集的信息用于以下用途：</view
      >

      <view class="list-item"
        >2.1 提供核心服务：职位匹配、房源推荐、零工分配、交友匹配等</view
      >
      <view class="list-item"
        >2.2 身份验证和安全防范：防止欺诈、虚假信息、恶意行为</view
      >
      <view class="list-item"
        >2.3 服务优化和改进：分析用户行为，优化算法推荐精准度</view
      >
      <view class="list-item">2.4 客户服务：处理咨询、投诉、纠纷等</view>
      <view class="list-item">2.5 数据分析：生成匿名化的数据统计报告</view>
      <view class="list-item"
        >2.6 营销推广：在获得您同意的前提下，推送相关服务信息</view
      >
      <view class="list-item"
        >2.7 法律合规：满足法律法规要求，配合监管调查</view
      >

      <view class="subtitle">三、信息共享、转让、公开披露</view>
      <view class="paragraph"
        >我们不会与本地宝平台以外的任何公司、组织和个人共享您的个人信息，但以下情况除外：</view
      >

      <view class="section-title">3.1 获得您的明确同意</view>
      <view class="paragraph"
        >在获得您的明确同意后，我们会与其他方共享您的个人信息。</view
      >

      <view class="section-title">3.2 法律法规要求</view>
      <view class="paragraph"
        >根据法律法规规定、诉讼争议解决需要，或按政府主管部门的强制性要求，对外共享您的个人信息。</view
      >

      <view class="section-title">3.3 业务合作伙伴</view>
      <view class="list-item">支付服务商：处理您的支付信息</view>
      <view class="list-item">地图服务商：提供位置和导航服务</view>
      <view class="list-item">云存储服务商：存储您的数据</view>
      <view class="list-item">短信服务商：发送验证码和通知消息</view>
      <view class="list-item">身份认证服务商：进行实名认证</view>

      <view class="section-title">3.4 业务转让</view>
      <view class="paragraph">
        如发生合并、收购或破产清算时，如涉及到个人信息转让，我们会要求新的持有您个人信息的公司、组织继续受本政策的约束，否则我们将要求该公司、组织重新向您征求授权同意。
      </view>

      <view class="subtitle">四、信息保护措施</view>
      <view class="paragraph"
        >我们已使用符合业界标准的安全防护措施保护您提供的个人信息：</view
      >

      <view class="list-item"
        >4.1 数据加密：采用SSL/TLS加密技术保护数据传输</view
      >
      <view class="list-item"
        >4.2 访问控制：严格限制有权访问个人信息的人员范围</view
      >
      <view class="list-item">4.3 安全审计：定期进行安全检测和漏洞扫描</view>
      <view class="list-item">4.4 数据备份：建立数据备份和灾难恢复机制</view>
      <view class="list-item">4.5 员工培训：定期进行数据保护和隐私培训</view>
      <view class="list-item"
        >4.6 安全认证：遵循ISO27001等信息安全管理标准</view
      >

      <view class="subtitle">五、信息存储</view>
      <view class="section-title">5.1 存储地点</view>
      <view class="paragraph">
        您的个人信息将存储在中华人民共和国境内。如需跨境传输，我们将会单独征得您的授权同意。
      </view>

      <view class="section-title">5.2 存储期限</view>
      <view class="list-item">账户信息：账户注销后保留30天</view>
      <view class="list-item">交易记录：保留3年，用于处理可能的争议</view>
      <view class="list-item">聊天记录：保留1年</view>
      <view class="list-item">日志信息：保留6个月</view>
      <view class="list-item">其他信息：根据法律法规要求的最短期限保存</view>

      <view class="subtitle">六、您的权利</view>
      <view class="paragraph"> 您对自己的个人信息享有以下权利： </view>

      <view class="list-item"
        >6.1 访问权：您有权了解我们收集、使用您个人信息的情况</view
      >
      <view class="list-item"
        >6.2 更正权：发现个人信息错误时，有权要求我们更正</view
      >
      <view class="list-item"
        >6.3 删除权：在特定情况下，您有权要求我们删除您的个人信息</view
      >
      <view class="list-item"
        >6.4 撤回同意：您有权撤回对个人信息处理的同意</view
      >
      <view class="list-item">6.5 投诉举报：您有权向监管部门投诉举报</view>
      <view class="list-item"
        >6.6
        数据可携带：您有权要求我们按照您的要求，向您或您指定的第三方转移个人信息</view
      >

      <view class="subtitle">七、未成年人信息保护</view>
      <view class="paragraph">
        我们非常重视对未成年人个人信息的保护。如果您是18周岁以下的未成年人，在使用我们的服务前，应事先取得您的家长或法定监护人的同意。如果您是未成年人的监护人，请您关注未成年人是否是在取得您的授权同意之后使用我们的服务。
      </view>

      <view class="subtitle">八、第三方服务</view>
      <view class="paragraph">
        我们的服务可能包含第三方提供的链接、服务或功能：
      </view>

      <view class="list-item"
        >8.1 微信登录：使用微信开放平台提供的登录服务</view
      >
      <view class="list-item">8.2 支付服务：使用微信支付等第三方支付平台</view>
      <view class="list-item"
        >8.3 地图服务：使用腾讯地图、高德地图等位置服务</view
      >
      <view class="list-item">8.4 短信服务：使用第三方短信平台发送验证码</view>

      <view class="paragraph">
        这些第三方服务由相应的第三方负责运营。您使用该等第三方服务时，除了要遵守本政策外，还应遵守第三方的隐私条款。
      </view>

      <view class="subtitle">九、Cookie和同类技术</view>
      <view class="paragraph">
        我们可能使用Cookie和同类技术来收集和存储您访问我们服务时的信息，这些技术帮助我们：
      </view>

      <view class="list-item">9.1 记住您的登录状态</view>
      <view class="list-item">9.2 保存您的偏好设置</view>
      <view class="list-item">9.3 分析服务使用情况</view>
      <view class="list-item">9.4 优化用户体验</view>

      <view class="subtitle">十、隐私政策的变更</view>
      <view class="paragraph">
        我们可能适时修订本隐私政策的条款。当隐私政策发生变更时，我们会在版本更新时以推送通知、弹窗的形式向您展示变更后的隐私政策。
      </view>

      <view class="paragraph">
        对于重大变更，我们还会提供更为显著的通知（包括对于某些服务，我们会通过电子邮件发送通知，说明隐私政策的具体变更内容）。
      </view>

      <view class="subtitle">十一、数据安全事件处理</view>
      <view class="paragraph">
        在不幸发生个人信息安全事件后，我们将按照法律法规的要求，及时向您告知：
      </view>

      <view class="list-item">安全事件的基本情况和可能的影响</view>
      <view class="list-item">我们已采取或将要采取的处置措施</view>
      <view class="list-item">您可自主防范和降低风险的建议</view>
      <view class="list-item">对您的补救措施等</view>

      <view class="paragraph">
        我们将及时将事件相关情况以邮件、信函、电话、推送通知等方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告。
      </view>

      <view class="subtitle">十二、免责声明</view>
      <view class="paragraph">
        <text class="important-text"
          >重要提示：以下免责条款请您仔细阅读并理解</text
        >
      </view>

      <view class="section-title">12.1 平台性质说明</view>
      <view class="paragraph">
        本地宝平台仅作为信息发布和展示平台，为用户提供信息交流和匹配服务。我们不是交易的参与方，不对用户间的实际交易过程、交易结果、服务质量等承担责任。
      </view>

      <view class="section-title">12.2 用户信息真实性</view>
      <view class="paragraph">
        我们会通过技术手段和人工抽查等方式对用户发布的信息进行基础的、形式上的审核，以过滤明显违法违规的内容，但我们无法也无义务保证所有用户信息的完全真实、准确和完整。用户应自行判断信息的可信度，并承担相应风险。
      </view>

      <view class="section-title">12.3 第三方服务免责</view>
      <view class="paragraph">
        对于通过我们平台链接到的第三方网站、应用程序或服务，我们不对其内容、隐私政策或做法负责。您与第三方的任何交互都由您自己承担风险。
      </view>

      <view class="section-title">12.4 技术故障免责</view>
      <view class="paragraph">
        对于因以下原因导致的服务中断或数据丢失，我们不承担责任：
      </view>

      <view class="list-item">系统维护、升级或故障</view>
      <view class="list-item">网络设备故障、网络阻塞</view>
      <view class="list-item">计算机病毒、恶意代码攻击</view>
      <view class="list-item">政府行为、自然灾害等不可抗力</view>
      <view class="list-item">用户的操作错误或设备问题</view>

      <view class="section-title">12.5 损失赔偿限制</view>
      <view class="paragraph">
        在法律允许的最大范围内，我们对因使用本服务而产生的任何直接、间接、偶然、特殊或后果性损害概不负责，包括但不限于：
      </view>

      <view class="list-item">利润损失、营业中断</view>
      <view class="list-item">数据丢失或损坏</view>
      <view class="list-item">替代服务的费用</view>
      <view class="list-item">其他经济损失</view>

      <view class="paragraph">
        即使我们已被告知此类损害的可能性，我们的总赔偿责任也不会超过您在过去12个月内为使用我们服务而支付的金额。
      </view>

      <view class="subtitle">十三、争议解决</view>
      <view class="section-title">13.1 适用法律</view>
      <view class="paragraph">
        本隐私政策的解释、效力及纠纷的解决，适用于中华人民共和国法律。如发生本隐私政策与中华人民共和国法律相冲突的情况，则这些条款将完全按法律规定重新解释。
      </view>

      <view class="section-title">13.2 争议解决方式</view>
      <view class="paragraph">
        若您和我们之间发生任何纠纷或争议，首先应友好协商解决；协商不成的，您同意将纠纷或争议提交至本地宝平台所在地有管辖权的人民法院诉讼解决。
      </view>

      <view class="section-title">13.3 条款独立性</view>
      <view class="paragraph">
        本隐私政策的任何条款如被有管辖权的法院认定为不可执行，该条款将被删除，但不影响其余条款的效力。
      </view>

      <view class="subtitle">十四、特别风险提示</view>
      <view class="warning-text">重要风险提示，请您特别注意：</view>

      <view class="list-item">网络交易存在风险，请谨慎选择交易对象</view>
      <view class="list-item"
        >不要轻易透露个人敏感信息（如银行卡号、密码等）</view
      >
      <view class="list-item">线下见面时请选择安全的公共场所</view>
      <view class="list-item">大额交易建议通过正规渠道并保留相关凭证</view>
      <view class="list-item">发现可疑行为请及时举报</view>

      <view class="subtitle">十五、监管合规</view>
      <view class="section-title">15.1 监管配合</view>
      <view class="paragraph">
        我们将严格遵守国家相关法律法规，包括但不限于《网络安全法》、《数据安全法》、《个人信息保护法》等，积极配合监管部门的监督检查。
      </view>

      <view class="section-title">15.2 数据本地化</view>
      <view class="paragraph">
        根据中华人民共和国相关法律法规要求，您的个人信息将在中华人民共和国境内进行收集和存储。如因业务需要确需向境外传输的，我们会按照相关规定进行安全评估并获得您的单独同意。
      </view>

      <view class="section-title">15.3 行业自律</view>
      <view class="paragraph">
        我们积极参与相关行业协会的自律活动，遵守行业规范，持续提升个人信息保护水平。
      </view>

      <view class="subtitle">十六、联系我们</view>
      <view class="paragraph">
        如果您对本隐私政策有任何疑问、意见或建议，或您需要访问、更正、删除您的个人信息，或撤回您的授权同意，请通过以下方式与我们联系：
      </view>

      <view class="contact-info">
        <view class="contact-title">数据保护联系方式</view>
        <view class="list-item">隐私政策专用邮箱：<EMAIL></view>
        <view class="list-item">数据保护热线：400-888-0001</view>
        <view class="list-item">在线客服：平台内联系客服</view>
        <view class="list-item">邮寄地址：[具体公司地址] 数据保护部门收</view>
        <view class="list-item">工作时间：周一至周日 9:00-21:00</view>
      </view>

      <view class="important-text">处理时效承诺：</view>
      <view class="paragraph"
        >一般情况下，我们将在15个工作日内回复您的请求。对于复杂请求，我们可能需要更多时间，但不会超过30个工作日，我们会及时向您说明情况。</view
      >

      <view class="paragraph">
        如果您对我们的回复不满意，特别是我们的个人信息处理行为损害了您的合法权益，您还可以通过以下途径寻求解决：
      </view>

      <view class="list-item">向所在地网信办投诉举报</view>
      <view class="list-item">向工业和信息化部投诉</view>
      <view class="list-item">向市场监督管理部门投诉</view>
      <view class="list-item">向消费者协会投诉</view>
      <view class="list-item">向人民法院提起诉讼</view>

      <view class="final-notice">
        <view class="important-text">最终声明：</view>
        <view class="paragraph"
          >本隐私政策的最终解释权在法律允许的最大范围内归本地宝平台所有。我们将按照本政策保护您的个人信息，如因我们的原因违反本政策，将依法承担相应的法律责任。</view
        >
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from "@/components/CustomNavBar.vue";
</script>

<style lang="scss" scoped>
.privacy-policy-page {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: 40rpx;
}

.content {
  padding: 32rpx;
}

.title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: var(--text-base);
  text-align: center;
  margin-bottom: 20rpx;
}

.update-info {
  display: block;
  font-size: 24rpx;
  color: var(--text-placeholder);
  text-align: center;
  margin-bottom: 40rpx;
  padding: 16rpx;
  background: var(--bg-secondary);
  border-radius: 8rpx;
}

.subtitle {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-top: 40rpx;
  margin-bottom: 20rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid var(--primary);
}

.section-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-top: 24rpx;
  margin-bottom: 16rpx;
}

.paragraph {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 20rpx;
  text-align: justify;
}

.list-item {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 12rpx;
  margin-left: 24rpx;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: -16rpx;
    top: 18rpx;
    width: 6rpx;
    height: 6rpx;
    background-color: var(--primary);
    border-radius: 50%;
  }
}

.important-text {
  color: var(--text-base);
  font-weight: 600;
  font-size: 30rpx;
}

.warning-text {
  color: #ff4757;
  font-weight: 600;
  font-size: 30rpx;
  margin-bottom: 20rpx;
  display: block;
}

.contact-info {
  background: var(--bg-secondary);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 20rpx 0;
  border-left: 4rpx solid var(--primary);

  .list-item {
    margin-left: 0;

    &::before {
      display: none;
    }
  }
}

.contact-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-bottom: 16rpx;
}

.final-notice {
  background: var(--bg-secondary);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 32rpx 0;
  border: 1rpx solid var(--border-color);
  text-align: center;

  .important-text {
    display: block;
    margin-bottom: 16rpx;
    color: var(--text-base);
    font-weight: 600;
  }

  .paragraph {
    color: var(--text-secondary);
    font-size: 28rpx;
    line-height: 1.6;
    margin-bottom: 0;
    text-align: center;
  }
}
</style>