<template>
  <view class="login-page">
    <view class="gradient-bg"></view>

    <view class="content">
      <view class="logo-section">
        <image src="/static/logo.png" class="app-logo"></image>
        <text class="app-name">零工平台</text>
      </view>

      <view class="welcome-section">
        <text class="welcome-title">欢迎来到零工平台</text>
        <text class="welcome-subtitle">一站式零工服务，轻松找活，高效招人</text>
      </view>

      <view class="login-section">
        <button class="wechat-login-btn" @tap="wechatLogin">
          <text class="i-carbon-logo-weixin wechat-icon"></text>
          <text>微信一键登录</text>
        </button>

        <view class="agreement-section">
          <view class="checkbox-wrapper" @tap="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreedToTerms }">
              <text v-if="agreedToTerms" class="i-carbon-checkmark"></text>
            </view>
          </view>
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @tap.stop="goToUserAgreement"
              >《用户注册协议》</text
            >
            和
            <text class="agreement-link" @tap.stop="goToPrivacyPolicy"
              >《隐私政策》</text
            >
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

const agreedToTerms = ref(false);

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const wechatLogin = () => {
  if (!agreedToTerms.value) {
    uni.showToast({
      title: "请先阅读并同意相关协议",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  uni.showLoading({
    title: "登录中...",
  });

  // Simulate WeChat login process
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: "登录成功",
      icon: "success",
      duration: 1500,
    });
    // Navigate to home page or previous page
    uni.reLaunch({
      url: "/pages/index/index",
    });
  }, 2000);
};

const goToUserAgreement = () => {
  uni.navigateTo({
    url: "/pages/auth/user-agreement",
  });
};

const goToPrivacyPolicy = () => {
  uni.navigateTo({
    url: "/pages/auth/privacy-policy",
  });
};
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    /* 左上角弥散渐变 */ radial-gradient(
      ellipse 80% 60% at 0% 0%,
      rgba(233, 222, 250, 0.8) 0%,
      rgba(233, 222, 250, 0.4) 40%,
      transparent 70%
    ),
    /* 右上角弥散渐变 */
      radial-gradient(
        ellipse 80% 60% at 100% 0%,
        rgba(251, 252, 219, 0.8) 0%,
        rgba(251, 252, 219, 0.4) 40%,
        transparent 70%
      ),
    /* 上半屏到下半屏的垂直过渡 */
      linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 40%,
        var(--bg-page) 60%
      );
  z-index: -1;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 200rpx 80rpx;
  width: 100%;
}

.logo-section {
  margin-bottom: 80rpx;
  text-align: center;
}

.app-logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 20rpx;
  border-radius: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-base);
  text-shadow: none;
}

.welcome-section {
  margin-bottom: 100rpx;
  text-align: center;
}

.welcome-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-base);
  margin-bottom: 16rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.login-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.wechat-login-btn {
  width: 100%;
  height: 100rpx;
  background-color: #28a745; /* WeChat green */
  color: white;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }

  &::after {
    border: none;
  }
}

.wechat-icon {
  font-size: 48rpx;
}

.agreement-section {
  margin-top: 60rpx;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  font-size: 26rpx;
  line-height: 1.5;
}

.checkbox-wrapper {
  flex-shrink: 0;
  padding-top: 4rpx;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &.checked {
    background-color: white;
    border-color: var(--text-blue);
    .i-carbon-checkmark {
      color: #28a745; /* WeChat green for checkmark */
      font-size: 28rpx;
    }
  }
}

.agreement-text {
  flex: 1;
}

.agreement-link {
  color: var(--text-blue);
  font-weight: bold;
}
</style>
