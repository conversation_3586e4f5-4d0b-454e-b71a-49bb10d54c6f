<template>
  <view class="container">
    <!-- 聊天消息区域 -->
    <z-paging
      ref="chatPagingRef"
      v-model="chatMessages"
      :autoToBottomWhenChat="true"
      :fixed="true"
      :show-refresher-when-reload="false"
      scrollWithAnimation
      use-chat-record-mode
      :bottom-bg-color="'#f8f8f8'"
      @query="queryList"
      @keyboardHeightChange="keyboardHeightChange"
      @hidedKeyboard="hidedKeyboard"
      class="chat-container"
    >
      <template #top>
        <uni-nav-bar
          title="聊天"
          statusBar
          fixed
          :border="false"
          left-icon="left"
          @click-left="goBack"
        />
      </template>

      <!-- 聊天记录日期 - 为了解决倒置问题，添加一个额外的容器 -->
      <!-- <view class="chat-date">
        <view class="chat-date-content" style="transform: scaleY(-1)">
          <text>{{ formattedDate }}</text>
        </view>
      </view> -->

      <!-- 消息列表 -->
      <template v-for="(message, index) in chatMessages" :key="message.id">
        <!-- 消息项 -->
        <view style="position: relative">
          <view class="flex-col" style="transform: scaleY(-1)">
            <!-- 显示时间，仅当距上一条消息超过10分钟时显示 -->
            <view
              class="chat-time"
              style="position: relative"
              v-if="shouldShowTime(message, index)"
            >
              <text class="time-text">{{
                formatMessageTime(message.time)
              }}</text>
            </view>
            <message-item
              :message="message"
              :other-avatar="
                message.from === 'other'
                  ? message.from_user_avatar
                  : message.to_user_avatar
              "
              :self-avatar="
                message.from === 'self'
                  ? message.from_user_avatar
                  : message.to_user_avatar
              "
              @preview-image="previewImage"
              @play-voice="playVoice"
              @open-location="openLocation"
              @resend="resendMessage"
              @copy="onCopyMessage"
            />
          </view>
        </view>
        <!-- 显示时间，仅当距上一条消息超过10分钟时显示 -->
        <!-- <view class="chat-time" style="position: relative" v-if="shouldShowTime(message, index)">
          <text class="time-text">{{ formatMessageTime(message.time) }}</text>
        </view> -->
      </template>

      <!-- 输入工具栏 -->
      <template #bottom>
        <chat-toolbar
          ref="inputBarRef"
          @send-text="sendTextMessage"
          @send-voice="sendVoiceMessage"
          @toggle-emoji-panel="onToggleEmojiPanel"
          @toggle-more-panel="onToggleMorePanel"
          @start-recording="startRecording"
          @end-recording="endRecording"
          @update-recording-status="updateRecordingStatus"
          @more-item-click="handleMoreItemClick"
        />
      </template>
    </z-paging>

    <!-- 语音录制遮罩层 -->
    <view
      class="voice-record-mask"
      v-if="isRecording"
      @touchmove.prevent
      @touchstart.prevent
      @touchend.prevent
    >
      <view class="voice-recording-container">
        <view class="voice-wave-container" :class="{ 'cancel-mode': isCancel }">
          <view
            class="wave-item"
            v-for="(item, index) in 3"
            :key="index"
            :style="{ animationDelay: `${index * 0.2}s` }"
          ></view>
        </view>
        <text class="voice-tip">{{ recordingTip }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import MessageItem from "./components/MessageItem.vue";
import ChatToolbar from "./components/ChatToolbar.vue";
import type { ChatMsg } from "@/types/chatMsg";

// 定义类型别名，便于使用
type Message = ChatMsg.Message;
type TextMessage = ChatMsg.TextMessage;
type ImageMessage = ChatMsg.ImageMessage;
type VoiceMessage = ChatMsg.VoiceMessage;
type LocationMessage = ChatMsg.LocationMessage;

// 工具栏引用
const inputBarRef = ref<InstanceType<typeof ChatToolbar> | null>(null);

// 聊天相关信息
const chatInfo = ref({
  id: 0,
  username: "",
  avatar: "",
});

// 聊天页面标题
const chatTitle = computed(() => chatInfo.value.username || "聊天");

// 控制面板显示
const showEmojiPanel = ref(false);
const showMorePanel = ref(false);

// 滚动位置
const scrollTop = ref(0);
const chatPagingRef = ref(null);

// 格式化的日期
const formattedDate = computed(() => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(now.getDate()).padStart(2, "0")} ${String(
    now.getHours()
  ).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}`;
});

// 模拟聊天消息
const chatMessages = ref<Message[]>([]);

// 键盘高度
const keyboardHeight = ref(0);

// 语音录制状态
const isRecording = ref(false);
const isCancel = ref(false);
const recordingTip = ref("上滑取消发送");

// 页面加载
onLoad((options: any) => {
  if (options) {
    chatInfo.value = {
      id: parseInt(options.id) || 0,
      username: decodeURIComponent(options.username || ""),
      avatar: decodeURIComponent(options.avatar || ""),
    };
  }

  // 加载初始聊天记录
  nextTick(() => {
    if (chatPagingRef.value) {
      chatPagingRef.value.scrollToBottom(true);
    }
  });
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示操作菜单
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ["清空聊天记录", "投诉", "加入黑名单", "删除好友"],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          // 清空聊天记录
          uni.showModal({
            title: "提示",
            content: "确定要清空聊天记录吗？",
            success: (res) => {
              if (res.confirm) {
                chatMessages.value = [];
              }
            },
          });
          break;
        case 1:
          // 投诉
          uni.showToast({
            title: "已提交投诉",
            icon: "none",
          });
          break;
        case 2:
          // 加入黑名单
          uni.showToast({
            title: "已加入黑名单",
            icon: "none",
          });
          break;
        case 3:
          // 删除好友
          uni.showModal({
            title: "提示",
            content: "确定要删除该好友吗？",
            success: (res) => {
              if (res.confirm) {
                uni.showToast({
                  title: "已删除好友",
                  icon: "none",
                });
                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              }
            },
          });
          break;
      }
    },
  });
};

// 查询聊天记录列表
const queryList = (pageNo: number, pageSize: number) => {
  // 模拟请求服务器获取分页数据
  setTimeout(() => {
    // 生成模拟数据
    const newMessages = generateMockMessages(pageNo, pageSize);

    // 将请求的结果数组传递给z-paging
    if (chatPagingRef.value) {
      chatPagingRef.value.complete(newMessages);
    }
  }, 500);
};

// 生成模拟消息数据
const generateMockMessages = (pageNo: number, pageSize: number): Message[] => {
  const messages: Message[] = [];
  const startId = (pageNo - 1) * pageSize;

  // 会话ID
  const conversationId = "conv_" + chatInfo.value.id;

  // 聊天参与者信息
  const selfInfo = {
    id: "10001",
    name: "我",
    avatar: "https://picsum.photos/seed/me/100",
  };

  const otherInfo = {
    id: chatInfo.value.id.toString() || "10002",
    name: chatInfo.value.username || "张三",
    avatar: chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
  };

  // 随机生成消息发送时间
  const generateTimestamp = (id: number) => {
    const now = new Date();
    // 每条消息间隔随机1-15分钟
    const randomInterval = Math.floor(Math.random() * 14 + 1) * 60 * 1000;
    // 基础间隔10分钟
    const baseInterval = 10 * 60 * 1000;
    return now.getTime() - (id * baseInterval + randomInterval);
  };

  // 根据页码生成不同的消息类型
  for (let i = 0; i < pageSize; i++) {
    const id = startId + i;

    // 正常消息是否由自己发送
    const isSelf = id % 2 === 0;

    // 消息目标类型
    const toType = isSelf ? ("other" as const) : ("self" as const);
    const toId = isSelf ? otherInfo.id : selfInfo.id;
    const toAvatar = isSelf ? otherInfo.avatar : selfInfo.avatar;
    const toName = isSelf ? otherInfo.name : selfInfo.name;

    // 消息发送者
    const fromType = isSelf ? ("self" as const) : ("other" as const);
    const fromId = isSelf ? selfInfo.id : otherInfo.id;
    const fromName = isSelf ? selfInfo.name : otherInfo.name;
    const fromAvatar = isSelf ? selfInfo.avatar : otherInfo.avatar;

    // 生成消息时间戳
    const timestamp = generateTimestamp(id);

    // 消息类型分布:
    // 0: 文本(50%), 1: 图片(25%), 2: 语音(15%), 3: 位置(10%)
    const messageTypeRandom = Math.random() * 100;
    let messageType: number;

    if (messageTypeRandom < 50) {
      messageType = 0; // 文本
    } else if (messageTypeRandom < 75) {
      messageType = 1; // 图片
    } else if (messageTypeRandom < 90) {
      messageType = 2; // 语音
    } else {
      messageType = 3; // 位置
    }

    // 基础消息属性
    const baseMessage = {
      id,
      conversationId,
      messageId: `msg_${id}_${Date.now()}`,
      clientMsgId: `client_${Date.now()}_${id}`,
      from: fromType,
      to: toType,
      fromId: fromId,
      toId: toId,
      from_user_avatar: fromAvatar,
      from_user_id: parseInt(fromId),
      from_user_name: fromName,
      to_user_avatar: toAvatar,
      to_user_id: parseInt(toId),
      to_user_name: toName,
      time: timestamp.toString(),
      status: "sent" as const,
      isRead: isSelf ? Math.random() > 0.5 : true, // 自己发送的消息有50%已读
    };

    switch (messageType) {
      case 0: // 文本消息
        const textContents = [
          `这是第${id}条文本消息，用于测试聊天记录加载功能。`,
          `今天天气真不错，适合出去钓鱼！`,
          `你用什么鱼饵钓的？我最近用蚯蚓效果不太好。`,
          `上次去西湖钓了一天，收获颇丰！`,
          `周末有空一起去钓鱼吗？我发现了一个新的好地方。`,
          `这款鱼竿怎么样？值得入手吗？`,
          `你平时都钓什么鱼？我比较喜欢钓鲫鱼。`,
          `最近钓场人太多了，建议早点去。`,
          `你有没有用过这个牌子的鱼线？耐用吗？`,
          `昨天钓了一条大鲤鱼，足足有五斤重！`,
        ];

        messages.push({
          ...baseMessage,
          type: "text",
          content:
            textContents[Math.floor(Math.random() * textContents.length)],
        } as TextMessage);
        break;

      case 1: // 图片消息
        const imageWidth = Math.floor(Math.random() * 600) + 400; // 400-1000
        const imageHeight = Math.floor(Math.random() * 600) + 400; // 400-1000
        const imageSize = Math.floor(Math.random() * 2000000) + 500000; // 500KB-2.5MB
        const imageFormats = ["webp"];
        const randomFormat =
          imageFormats[Math.floor(Math.random() * imageFormats.length)];

        messages.push({
          ...baseMessage,
          type: "image",
          content: `https://picsum.photos/${imageWidth}/${imageHeight}`,
          ext: {
            width: imageWidth,
            height: imageHeight,
            size: imageSize,
            format: randomFormat,
          },
        } as ImageMessage);
        break;

      case 2: // 语音消息
        const voiceDuration = Math.floor(Math.random() * 60) + 1; // 1-60秒
        const voiceSize = voiceDuration * 1024 * 15; // 大约每秒15KB
        const audioFormats = ["mp3", "aac", "wav"];
        const randomAudioFormat =
          audioFormats[Math.floor(Math.random() * audioFormats.length)];

        messages.push({
          ...baseMessage,
          type: "voice",
          content: `voice_${id}.${randomAudioFormat}`,
          ext: {
            duration: voiceDuration,
            size: voiceSize,
            format: randomAudioFormat,
          },
        } as VoiceMessage);
        break;

      case 3: // 位置消息
        const locations = [
          {
            name: "西湖",
            address: "浙江省杭州市西湖区龙井路1号",
            latitude: 30.2591,
            longitude: 120.1503,
          },
          {
            name: "千岛湖",
            address: "浙江省杭州市淳安县千岛湖镇",
            latitude: 29.5893,
            longitude: 119.0437,
          },
          {
            name: "钱塘江",
            address: "浙江省杭州市上城区钱江路",
            latitude: 30.2084,
            longitude: 120.1918,
          },
          {
            name: "太湖",
            address: "江苏省苏州市吴中区太湖大道",
            latitude: 31.1667,
            longitude: 120.2667,
          },
          {
            name: "洞庭湖",
            address: "湖南省岳阳市岳阳楼区洞庭大道",
            latitude: 29.3126,
            longitude: 113.0089,
          },
        ];
        const randomLocation =
          locations[Math.floor(Math.random() * locations.length)];

        messages.push({
          ...baseMessage,
          type: "location",
          content: {
            name: randomLocation.name,
            address: randomLocation.address,
            latitude: randomLocation.latitude + (Math.random() * 0.01 - 0.005), // 添加一点随机偏移
            longitude:
              randomLocation.longitude + (Math.random() * 0.01 - 0.005),
            image: `https://picsum.photos/map_${id}/400/200`,
          },
        } as LocationMessage);
        break;
    }
  }

  return messages.sort((a, b) => parseInt(a.time) - parseInt(b.time));
};

// 判断是否应该显示时间
const shouldShowTime = (message: Message, index: number): boolean => {
  // 第一条消息总是显示时间
  if (index === 0) return true;

  // 获取当前消息和上一条消息的时间
  const currentTime = parseInt(message.time);
  const prevTime = parseInt(chatMessages.value[index - 1].time);

  // 如果与上一条消息相差超过10分钟(600000毫秒)，则显示时间
  return currentTime - prevTime > 600000;
};

// 格式化消息时间显示
const formatMessageTime = (timestamp: string): string => {
  const time = new Date(parseInt(timestamp));
  const today = new Date();

  // 提取小时和分钟
  const hours = time.getHours().toString().padStart(2, "0");
  const minutes = time.getMinutes().toString().padStart(2, "0");

  // 判断是否是今天
  if (
    time.getDate() === today.getDate() &&
    time.getMonth() === today.getMonth() &&
    time.getFullYear() === today.getFullYear()
  ) {
    return `${hours}:${minutes}`;
  }

  // 判断是否是昨天
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  if (
    time.getDate() === yesterday.getDate() &&
    time.getMonth() === yesterday.getMonth() &&
    time.getFullYear() === yesterday.getFullYear()
  ) {
    return `昨天 ${hours}:${minutes}`;
  }

  // 判断是否是本周
  const weekday = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const dayDiff = Math.floor(
    (today.getTime() - time.getTime()) / (24 * 60 * 60 * 1000)
  );
  if (dayDiff < 7) {
    return `${weekday[time.getDay()]} ${hours}:${minutes}`;
  }

  // 其他天显示日期和时间
  const month = (time.getMonth() + 1).toString().padStart(2, "0");
  const day = time.getDate().toString().padStart(2, "0");

  // 判断是否是今年
  if (time.getFullYear() === today.getFullYear()) {
    return `${month}月${day}日 ${hours}:${minutes}`;
  }

  // 不是今年
  return `${time.getFullYear()}年${month}月${day}日 ${hours}:${minutes}`;
};

// 发送文本消息
const sendTextMessage = (text: string) => {
  // 创建新消息
  const newMessage: TextMessage = {
    id: Date.now(),
    conversationId: "conv_" + chatInfo.value.id,
    messageId: `msg_${Date.now()}`,
    clientMsgId: `client_${Date.now()}`,
    from: "self",
    to: "other",
    fromId: "10001", // 自己的ID
    toId: chatInfo.value.id.toString() || "10002", // 对方的ID
    from_user_avatar: "https://picsum.photos/seed/me/100",
    from_user_id: 10001,
    from_user_name: "我",
    to_user_avatar:
      chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
    to_user_id: chatInfo.value.id || 10002,
    to_user_name: chatInfo.value.username || "张三",
    type: "text",
    content: text,
    time: Date.now().toString(),
    status: "sending",
    isRead: false,
  };

  // 添加到消息列表
  if (chatPagingRef.value) {
    chatPagingRef.value.addChatRecordData(newMessage);
  }

  // 模拟发送过程
  setTimeout(() => {
    // 模拟成功率90%
    const isSuccess = Math.random() > 0.1;
    const index = chatMessages.value.findIndex(
      (msg) => msg.id === newMessage.id
    );

    if (index !== -1) {
      chatMessages.value[index].status = isSuccess ? "sent" : "failed";
    }

    // 如果发送成功，模拟对方已读状态更新
    if (isSuccess) {
      setTimeout(() => {
        if (index !== -1) {
          chatMessages.value[index].isRead = true;
        }
      }, 2000 + Math.random() * 3000); // 2-5秒后已读
    }
  }, 1000);
};

// 切换表情面板
const onToggleEmojiPanel = (show: boolean) => {
  showEmojiPanel.value = show;

  // 确保表情面板显示时，键盘隐藏
  if (show) {
    uni.hideKeyboard();
  }
};

// 切换更多功能面板
const onToggleMorePanel = (show: boolean) => {
  showMorePanel.value = show;

  // 确保更多面板显示时，键盘隐藏
  if (show) {
    uni.hideKeyboard();
  }
};

// 处理更多面板项点击
const handleMoreItemClick = (type: string) => {
  switch (type) {
    case "album":
      chooseImage();
      break;
    case "camera":
      takePhoto();
      break;
    case "location":
      chooseLocation();
      break;
    case "call":
      startVoiceCall();
      break;
    case "note":
      shareNote();
      break;
    case "file":
      showToastInDev("文件功能开发中");
      break;
    case "favorite":
      showToastInDev("收藏功能开发中");
      break;
    case "contact":
      showToastInDev("名片功能开发中");
      break;
  }
};

// 显示开发中提示
const showToastInDev = (message: string) => {
  uni.showToast({
    title: message,
    icon: "none",
  });
};

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["album"],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];

      // 获取图片信息
      uni.getImageInfo({
        src: tempFilePath,
        success: (imageInfo) => {
          // 发送图片消息
          const newMessage: ImageMessage = {
            id: Date.now(),
            conversationId: "conv_" + chatInfo.value.id,
            messageId: `msg_${Date.now()}`,
            clientMsgId: `client_${Date.now()}`,
            from: "self",
            to: "other",
            fromId: "10001", // 自己的ID
            toId: chatInfo.value.id.toString() || "10002", // 对方的ID
            from_user_avatar: "https://picsum.photos/seed/me/100",
            from_user_id: 10001,
            from_user_name: "我",
            to_user_avatar:
              chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
            to_user_id: chatInfo.value.id || 10002,
            to_user_name: chatInfo.value.username || "张三",
            type: "image",
            content: tempFilePath,
            ext: {
              width: imageInfo.width,
              height: imageInfo.height,
              size: res.tempFiles[0].size || 0,
              format: tempFilePath.split(".").pop()?.toLowerCase() || "jpg",
            },
            time: Date.now().toString(),
            status: "sending",
            isRead: false,
          };

          if (chatPagingRef.value) {
            chatPagingRef.value.addChatRecordData(newMessage);
          }

          // 模拟上传过程
          setTimeout(() => {
            const isSuccess = Math.random() > 0.1;
            const index = chatMessages.value.findIndex(
              (msg) => msg.id === newMessage.id
            );

            if (index !== -1) {
              chatMessages.value[index].status = isSuccess ? "sent" : "failed";
            }

            // 如果发送成功，模拟对方已读状态更新
            if (isSuccess) {
              setTimeout(() => {
                if (index !== -1) {
                  chatMessages.value[index].isRead = true;
                }
              }, 2000 + Math.random() * 3000); // 2-5秒后已读
            }
          }, 1500);
        },
        fail: () => {
          // 获取图片信息失败，使用默认值
          const newMessage: ImageMessage = {
            id: Date.now(),
            conversationId: "conv_" + chatInfo.value.id,
            messageId: `msg_${Date.now()}`,
            clientMsgId: `client_${Date.now()}`,
            from: "self",
            to: "other",
            fromId: "10001",
            toId: chatInfo.value.id.toString() || "10002",
            from_user_avatar: "https://picsum.photos/seed/me/100",
            from_user_id: 10001,
            from_user_name: "我",
            to_user_avatar:
              chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
            to_user_id: chatInfo.value.id || 10002,
            to_user_name: chatInfo.value.username || "张三",
            type: "image",
            content: tempFilePath,
            ext: {
              width: 300,
              height: 300,
              size: res.tempFiles[0].size || 0,
              format: "jpg",
            },
            time: Date.now().toString(),
            status: "sending",
            isRead: false,
          };

          if (chatPagingRef.value) {
            chatPagingRef.value.addChatRecordData(newMessage);
          }

          // 模拟上传过程
          setTimeout(() => {
            const isSuccess = Math.random() > 0.1;
            const index = chatMessages.value.findIndex(
              (msg) => msg.id === newMessage.id
            );

            if (index !== -1) {
              chatMessages.value[index].status = isSuccess ? "sent" : "failed";
            }

            // 如果发送成功，模拟对方已读状态更新
            if (isSuccess) {
              setTimeout(() => {
                if (index !== -1) {
                  chatMessages.value[index].isRead = true;
                }
              }, 2000 + Math.random() * 3000); // 2-5秒后已读
            }
          }, 1500);
        },
      });
    },
  });
};

// 拍照
const takePhoto = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["camera"],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];

      // 获取图片信息
      uni.getImageInfo({
        src: tempFilePath,
        success: (imageInfo) => {
          // 发送图片消息
          const newMessage: ImageMessage = {
            id: Date.now(),
            conversationId: "conv_" + chatInfo.value.id,
            messageId: `msg_${Date.now()}`,
            clientMsgId: `client_${Date.now()}`,
            from: "self",
            to: "other",
            fromId: "10001", // 自己的ID
            toId: chatInfo.value.id.toString() || "10002", // 对方的ID
            from_user_avatar: "https://picsum.photos/seed/me/100",
            from_user_id: 10001,
            from_user_name: "我",
            to_user_avatar:
              chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
            to_user_id: chatInfo.value.id || 10002,
            to_user_name: chatInfo.value.username || "张三",
            type: "image",
            content: tempFilePath,
            ext: {
              width: imageInfo.width,
              height: imageInfo.height,
              size: res.tempFiles[0].size || 0,
              format: tempFilePath.split(".").pop()?.toLowerCase() || "jpg",
              thumbnail: tempFilePath, // 实际应用中应生成缩略图
            },
            time: Date.now().toString(),
            status: "sending",
            isRead: false,
          };

          if (chatPagingRef.value) {
            chatPagingRef.value.addChatRecordData(newMessage);
          }

          // 模拟上传过程
          setTimeout(() => {
            const isSuccess = Math.random() > 0.1;
            const index = chatMessages.value.findIndex(
              (msg) => msg.id === newMessage.id
            );

            if (index !== -1) {
              chatMessages.value[index].status = isSuccess ? "sent" : "failed";
            }
          }, 1500);
        },
        fail: () => {
          // 获取图片信息失败，使用默认值
          const newMessage: ImageMessage = {
            id: Date.now(),
            conversationId: "conv_" + chatInfo.value.id,
            messageId: `msg_${Date.now()}`,
            clientMsgId: `client_${Date.now()}`,
            from: "self",
            to: "other",
            fromId: "10001",
            toId: chatInfo.value.id.toString() || "10002",
            from_user_avatar: "https://picsum.photos/seed/me/100",
            from_user_id: 10001,
            from_user_name: "我",
            to_user_avatar:
              chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
            to_user_id: chatInfo.value.id || 10002,
            to_user_name: chatInfo.value.username || "张三",
            type: "image",
            content: tempFilePath,
            ext: {
              width: 300,
              height: 300,
              size: res.tempFiles[0].size || 0,
              format: "jpg",
              thumbnail: tempFilePath,
            },
            time: Date.now().toString(),
            status: "sending",
            isRead: false,
          };

          if (chatPagingRef.value) {
            chatPagingRef.value.addChatRecordData(newMessage);
          }

          // 模拟上传过程
          setTimeout(() => {
            const isSuccess = Math.random() > 0.1;
            const index = chatMessages.value.findIndex(
              (msg) => msg.id === newMessage.id
            );

            if (index !== -1) {
              chatMessages.value[index].status = isSuccess ? "sent" : "failed";
            }
          }, 1500);
        },
      });
    },
  });
};

// 选择位置
const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      // 发送位置消息
      const newMessage: LocationMessage = {
        id: Date.now(),
        conversationId: "conv_" + chatInfo.value.id,
        messageId: `msg_${Date.now()}`,
        clientMsgId: `client_${Date.now()}`,
        from: "self",
        to: "other",
        fromId: "10001", // 自己的ID
        toId: chatInfo.value.id.toString() || "10002", // 对方的ID
        from_user_avatar: "https://picsum.photos/seed/me/100",
        from_user_id: 10001,
        from_user_name: "我",
        to_user_avatar:
          chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
        to_user_id: chatInfo.value.id || 10002,
        to_user_name: chatInfo.value.username || "张三",
        type: "location",
        content: {
          name: res.name,
          address: res.address,
          latitude: res.latitude,
          longitude: res.longitude,
          image: "https://picsum.photos/seed/map/400/200", // 使用模拟地图图片
        },
        time: Date.now().toString(),
        status: "sending",
        isRead: false,
      };

      if (chatPagingRef.value) {
        chatPagingRef.value.addChatRecordData(newMessage);
      }

      // 模拟发送过程
      setTimeout(() => {
        const isSuccess = Math.random() > 0.1;
        const index = chatMessages.value.findIndex(
          (msg) => msg.id === newMessage.id
        );

        if (index !== -1) {
          chatMessages.value[index].status = isSuccess ? "sent" : "failed";
        }

        // 如果发送成功，模拟对方已读状态更新
        if (isSuccess) {
          setTimeout(() => {
            if (index !== -1) {
              chatMessages.value[index].isRead = true;
            }
          }, 2000 + Math.random() * 3000); // 2-5秒后已读
        }
      }, 1000);
    },
  });
};

// 发送语音消息
const sendVoiceMessage = (duration: number) => {
  // 创建新消息
  const voiceSize = duration * 1024 * 15; // 大约每秒15KB
  const audioFormats = ["mp3", "aac", "wav"];
  const randomAudioFormat =
    audioFormats[Math.floor(Math.random() * audioFormats.length)];

  const newMessage: VoiceMessage = {
    id: Date.now(),
    conversationId: "conv_" + chatInfo.value.id,
    messageId: `msg_${Date.now()}`,
    clientMsgId: `client_${Date.now()}`,
    from: "self",
    to: "other",
    fromId: "10001", // 自己的ID
    toId: chatInfo.value.id.toString() || "10002", // 对方的ID
    from_user_avatar: "https://picsum.photos/seed/me/100",
    from_user_id: 10001,
    from_user_name: "我",
    to_user_avatar:
      chatInfo.value.avatar || "https://picsum.photos/seed/other/100",
    to_user_id: chatInfo.value.id || 10002,
    to_user_name: chatInfo.value.username || "张三",
    type: "voice",
    content: `voice_file_${Date.now()}.${randomAudioFormat}`, // 实际项目中是录音文件路径
    ext: {
      duration: duration,
      size: voiceSize,
      format: randomAudioFormat,
    },
    time: Date.now().toString(),
    status: "sending",
    isRead: false,
  };

  if (chatPagingRef.value) {
    chatPagingRef.value.addChatRecordData(newMessage);
  }

  // 模拟发送过程
  setTimeout(() => {
    const isSuccess = Math.random() > 0.1;
    const index = chatMessages.value.findIndex(
      (msg) => msg.id === newMessage.id
    );

    if (index !== -1) {
      chatMessages.value[index].status = isSuccess ? "sent" : "failed";
    }

    // 如果发送成功，模拟对方已读状态更新
    if (isSuccess) {
      setTimeout(() => {
        if (index !== -1) {
          chatMessages.value[index].isRead = true;
        }
      }, 2000 + Math.random() * 3000); // 2-5秒后已读
    }
  }, 1000);
};

// 分享笔记
const shareNote = () => {
  uni.showToast({
    title: "分享笔记功能开发中",
    icon: "none",
  });
};

// 语音通话
const startVoiceCall = () => {
  uni.showToast({
    title: "语音通话功能开发中",
    icon: "none",
  });
};

// 播放语音
const playVoice = (message: VoiceMessage) => {
  uni.showToast({
    title: `播放语音消息: ${message.ext.duration}秒`,
    icon: "none",
  });
};

// 预览图片
const previewImage = (url: string) => {
  // 收集所有图片URL
  const imageUrls = chatMessages.value
    .filter((msg) => msg.type === "image")
    .map((msg) => msg.content);

  uni.previewImage({
    current: url,
    urls: imageUrls,
  });
};

// 打开位置
const openLocation = (location: LocationMessage["content"]) => {
  uni.openLocation({
    latitude: location.latitude,
    longitude: location.longitude,
    name: location.name,
    address: location.address,
  });
};

// 重新发送消息
const resendMessage = (message: Message) => {
  const index = chatMessages.value.findIndex((msg) => msg.id === message.id);

  if (index !== -1) {
    chatMessages.value[index].status = "sending";

    // 模拟重新发送过程
    setTimeout(() => {
      const isSuccess = Math.random() > 0.1;

      if (index !== -1) {
        chatMessages.value[index].status = isSuccess ? "sent" : "failed";
      }
    }, 1000);
  }
};

// 复制消息
const onCopyMessage = (content: string) => {
  uni.setClipboardData({
    data: content,
    success: () => {
      uni.showToast({
        title: "复制成功",
        icon: "none",
      });
    },
  });
};

// 键盘高度变化
const keyboardHeightChange = (res: any) => {
  // 调用输入框组件的方法处理键盘高度变化
  inputBarRef.value?.updateKeyboardHeightChange(res);

  // 当键盘弹出时，滚动到底部
  if (res.height > 0 && chatPagingRef.value) {
    chatPagingRef.value.scrollToBottom(true);
  }
};

// 隐藏键盘
const hidedKeyboard = () => {
  // 调用输入框组件的方法处理键盘隐藏事件
  inputBarRef.value?.hidedKeyboard();
};

// 开始录音
const startRecording = () => {
  isRecording.value = true;
  isCancel.value = false;
  recordingTip.value = "上滑取消发送";

  // 禁用z-paging滚动
  // if (chatPagingRef.value) {
  //   // 使用正确的方法禁用滚动
  //   chatPagingRef.value.scrollable = false
  // }

  // 添加震动反馈
  // uni.vibrateShort()
};

// 结束录音
const endRecording = (duration: number) => {
  isRecording.value = false;
  isCancel.value = false;

  // 启用z-paging滚动
  // if (chatPagingRef.value) {
  //   // 使用正确的方法启用滚动
  //   chatPagingRef.value.scrollable = true
  // }

  // // 添加震动反馈
  // uni.vibrateShort()

  if (!isCancel.value && duration > 1) {
    sendVoiceMessage(duration);
  }
};

// 更新录音状态
const updateRecordingStatus = (isCanceled: boolean) => {
  isCancel.value = isCanceled;
  recordingTip.value = isCanceled ? "松开手指取消发送" : "上滑取消发送";

  // 状态改变时添加震动反馈
  if (isCancel.value !== isCanceled) {
    // uni.vibrateShort()
  }
};
</script>

<style lang="scss" scoped>
::v-deep .uni-nav-bar-text {
  font-size: 34rpx !important;
  font-weight: 500 !important;
  color: #000;
}

.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(
    180deg,
    var(--bg-page) 0%,
    var(--bg-primary) 100%
  );
}

.chat-container {
  box-sizing: border-box;
  flex: 1;
  width: 100%;
  padding: 16rpx 0;
  overflow: hidden;
}

.chat-date {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
  /* 整个容器不需要倒置，内部内容倒置 */
}

.chat-date-content {
  padding: 4rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;

  text {
    font-size: 24rpx;
    color: #999;
  }
}
/* 消息时间样式 */
.chat-time {
  display: flex;
  justify-content: center;
  margin: 32rpx 0 24rpx;

  .time-text {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    color: #6b7280;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 16rpx;
    letter-spacing: 0.2rpx;
    font-weight: 400;
  }
}

.voice-record-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 200rpx;
  background-color: rgba(0, 0, 0, 0.5);
  animation: mask-fade-in 0.2s ease;
}

@keyframes mask-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.voice-recording-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  height: 300rpx;
  padding: 40rpx;
  margin-top: 60%;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  animation: container-scale 0.3s ease;
}

@keyframes container-scale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.voice-wave-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.wave-item {
  width: 4rpx;
  height: 40rpx;
  margin: 0 6rpx;
  background-color: #2ecc71;
  border-radius: 2rpx;
  animation: wave 1s infinite ease-in-out;
}

.voice-wave-container.cancel-mode .wave-item {
  background-color: #ff4d4f;
}

@keyframes wave {
  0%,
  100% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1.5);
  }
}

.voice-tip {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #fff;
}
</style>
