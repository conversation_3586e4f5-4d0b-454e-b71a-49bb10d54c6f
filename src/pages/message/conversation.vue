<template>
  <view class="container">
    <!-- 操作选项区域 -->
    <view class="message-options">
      <view class="option-item" @tap="handleOptionClick(0)">
        <view class="option-icon heart">
          <tui-icon name="heart-fill" color="#ff6b6b" size="24"></tui-icon>
        </view>
        <text class="option-text">赞和收藏</text>
      </view>
      <view class="option-item" @tap="handleOptionClick(1)">
        <view class="option-icon follow">
          <tui-icon name="people-fill" color="#5677fc" size="24"></tui-icon>
        </view>
        <text class="option-text">新增关注</text>
      </view>
      <view class="option-item" @tap="handleOptionClick(2)">
        <view class="option-icon comment">
          <tui-icon name="message-fill" color="#2fd0ac" size="24"></tui-icon>
        </view>
        <text class="option-text">评论和@</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <tui-list-view>
      <tui-list-cell
        v-for="(item, index) in messageList"
        :key="index"
        :hover="true"
        :unlined="index === messageList.length - 1"
        @click="handleMessageClick(item)"
      >
        <view class="message-item">
          <view class="avatar-container">
            <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
            <view class="status-dot" v-if="item.online"></view>
          </view>
          <view class="message-content">
            <view class="message-top">
              <text class="message-name">{{ item.name }}</text>
              <text class="message-time">{{ item.time }}</text>
            </view>
            <view class="message-bottom">
              <text class="message-text">{{ item.lastMessage }}</text>
              <view class="message-badge" v-if="item.unread > 0">
                <text>{{ item.unread > 99 ? "99+" : item.unread }}</text>
              </view>
              <view class="message-dot" v-if="item.unread === -1"></view>
            </view>
          </view>
        </view>
      </tui-list-cell>
    </tui-list-view>

    <!-- 无消息提示 -->
    <tui-no-data
      v-if="messageList.length === 0"
      imgUrl="/static/images/empty.png"
      btnText="去寻找好友"
      @click="goToFindFriends"
    ></tui-no-data>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// 消息类型定义
interface MessageItem {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  time: string;
  unread: number; // -1表示红点提示，0表示无提示，>0表示未读数量
  online: boolean;
  type: "text" | "image" | "video" | "audio" | "location";
}

// 消息列表数据
const messageList = ref<MessageItem[]>([
  {
    id: "1",
    name: "北京梵客全屋整装",
    avatar: "/static/images/avatar1.png",
    lastMessage: "哈喽哈喽 宝子咱家是毛坯房 还是老房子翻新啊",
    time: "11:08",
    unread: 1,
    online: true,
    type: "text",
  },
  {
    id: "2",
    name: "活动消息",
    avatar: "/static/images/notice.png",
    lastMessage: "嘻嘻北京的风光",
    time: "昨天",
    unread: -1,
    online: false,
    type: "text",
  },
  {
    id: "3",
    name: "紫陌教UXUI",
    avatar: "/static/images/avatar2.png",
    lastMessage: "[视频]",
    time: "03-12",
    unread: 0,
    online: false,
    type: "video",
  },
  {
    id: "4",
    name: "独立设计师宋宋",
    avatar: "/static/images/avatar3.png",
    lastMessage: "不客气哦",
    time: "03-10",
    unread: 0,
    online: true,
    type: "text",
  },
  {
    id: "5",
    name: "小A",
    avatar: "/static/images/avatar4.png",
    lastMessage: "你好呀～ 看到您对用户体验的话题感兴趣，想请教一下...",
    time: "03-08",
    unread: 0,
    online: false,
    type: "text",
  },
  {
    id: "6",
    name: "系统消息",
    avatar: "/static/images/system.png",
    lastMessage: "小红书诚邀您参加问卷小调研",
    time: "02-18",
    unread: 0,
    online: false,
    type: "text",
  },
  {
    id: "7",
    name: "菜心视觉设计 (招生中)",
    avatar: "/static/images/avatar5.png",
    lastMessage: "很多设计师努力几年，进步却很小，可能...",
    time: "01-17",
    unread: 0,
    online: false,
    type: "text",
  },
  {
    id: "8",
    name: "菜心设计铺 (招生中)",
    avatar: "/static/images/avatar6.png",
    lastMessage: "ui设计到底怎么搞!",
    time: "01-07",
    unread: -1,
    online: false,
    type: "text",
  },
]);

// 处理点击消息项
const handleMessageClick = (item: MessageItem) => {
  uni.navigateTo({
    url: `/pages/message/chat?id=${item.id}&name=${item.name}`,
  });
};

// 处理点击选项
const handleOptionClick = (index: number) => {
  const options = ["赞和收藏", "新增关注", "评论和@"];
  uni.showToast({
    title: `点击了${options[index]}`,
    icon: "none",
  });
};

// 前往寻找好友
const goToFindFriends = () => {
  uni.navigateTo({
    url: "/pages/index/index",
  });
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 120rpx;
}

.action-text {
  font-size: 28rpx;
  margin-left: 6rpx;
}

.message-options {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
}

.heart {
  background-color: rgba(255, 107, 107, 0.1);
}

.follow {
  background-color: rgba(86, 119, 252, 0.1);
}

.comment {
  background-color: rgba(47, 208, 172, 0.1);
}

.option-text {
  font-size: 26rpx;
}

.message-item {
  display: flex;
  width: 100%;
}

.avatar-container {
  position: relative;
  margin-right: 20rpx;
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.status-dot {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 16rpx;
  height: 16rpx;
  background-color: #07c160;
  border: 2rpx solid #ffffff;
  border-radius: 50%;
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.message-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.message-name {
  font-size: 30rpx;
  font-weight: 500;
}

.message-time {
  font-size: 24rpx;
  color: var(--text-info);
}

.message-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-text {
  flex: 1;
  font-size: 26rpx;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-badge {
  min-width: 36rpx;
  height: 36rpx;
  padding: 0 10rpx;
  background-color: #e94242;
  border-radius: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.message-badge text {
  color: #fff;
  font-size: 22rpx;
}

.message-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #e94242;
  border-radius: 50%;
}
</style>
