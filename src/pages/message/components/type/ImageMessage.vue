<template>
  <view class="image-message" :class="{ self: self }" @click="onImageClick">
    <!-- <image
      :src="src"
      mode="widthFix"
      lazy-load
      class="image"
      :style="{ width: imgWidth, height: imgHeight }"
      fade-show
      @load="onImageLoad"
      @error="onImageError"
    /> -->
    <network-image
      :src="src"
      mode="widthFix"
      :width="imgWidth"
      :height="imgHeight"
    />
  </view>
</template>

<script setup lang="ts">
import NetworkImage from "@/components/NetworkImage.vue";
const staticBaseUrl = import.meta.env.VITE_STATIC_BASEURL;
const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  self: {
    type: Boolean,
    required: true,
  },
  width: {
    type: Number,
    required: true,
  },
  height: {
    type: Number,
    required: true,
  },
  maxWidth: {
    type: Number,
    default: 400,
  },
  maxHeight: {
    type: Number,
    default: 400,
  },
});

const imgWidth = ref("400rpx");
const imgHeight = ref("400rpx");
const imageLoaded = ref(false);
const isLoading = ref(true);
const isError = ref(false);

onMounted(() => {
  if (props.width && props.height) {
    // 如果宽超过最大宽度，高度按比例缩小
    if (props.width > props.maxWidth) {
      imgWidth.value = props.maxWidth + "rpx";
      imgHeight.value =
        ((props.height * props.maxWidth) / props.width).toFixed(1) + "rpx";
    }

    // 如果高超过最大高度，宽度按比例缩小
    if (props.height > props.maxHeight) {
      imgWidth.value =
        ((props.width * props.maxHeight) / props.height).toFixed(1) + "rpx";
      imgHeight.value = props.maxHeight + "rpx";
    }
  }
  console.log("imgWidth", imgWidth.value);
  console.log("imgHeight", imgHeight.value);
});

// 图片加载完成
const onImageLoad = (e: any) => {
  // 如果没有传入宽高，就使用图片的原始宽高
  // if (!props.width || !props.height) {
  //   const { width, height } = e.detail
  //   imgWidth.value = width
  //   imgHeight.value = height
  // } else {
  //   imgWidth.value = props.width + 'rpx'
  //   imgHeight.value = props.height + 'rpx'
  // }

  isError.value = false;
  imageLoaded.value = true;
};

// 图片加载失败
const onImageError = () => {
  console.error("图片加载失败:", props.src);
  isLoading.value = false;
  isError.value = true;
};

// 重新加载图片
const reloadImage = () => {
  isLoading.value = true;
  isError.value = false;

  // 添加时间戳或随机参数以避免浏览器缓存
  const timestamp = Date.now();
  const url = new URL(props.src, window.location.href);

  if (url.search) {
    url.searchParams.append("_t", timestamp.toString());
  } else if (props.src.indexOf("?") > -1) {
    url.searchParams.append("_t", timestamp.toString());
  }

  // 创建一个新的Image对象尝试预加载
  const img = new Image();
  img.onload = () => {
    isLoading.value = false;
    isError.value = false;
    imageLoaded.value = true;
  };
  img.onerror = () => {
    isLoading.value = false;
    isError.value = true;
  };
  img.src = props.src;
};

// 图片点击处理
const onImageClick = () => {
  console.log("onImageClick", isError.value);
  if (isError.value) {
    reloadImage();
    return;
  }
  preview();
  // if (!isLoading.value && !isError.value) {
  //   preview()
  // }
};

// 预览图片
const preview = () => {
  uni.previewImage({
    urls: [props.src],
    current: props.src,
    showmenu: true,
    success: () => {
      console.log("预览图片成功");
    },
    fail: (err) => {
      console.error("预览图片失败", err);
      uni.showToast({
        title: "预览图片失败",
        icon: "none",
      });
    },
  });
};
</script>

<style lang="scss" scoped>
.image-message {
  display: inline-block;
  align-self: flex-start;
  max-width: 480rpx;
  max-height: 480rpx;
  overflow: hidden;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

  &.self {
    align-self: flex-end;
  }

  .image {
    display: block;
    border-radius: 16rpx;
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .error-placeholder {
    cursor: pointer;
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 24rpx;
    border-radius: 16rpx;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
