<template>
  <view class="bubble-box" :class="{ self }">
    <!-- 发送者头像容器 -->
    <view class="avatar-container">
      <image class="avatar" :src="avatarUrl" mode="aspectFill" />
    </view>

    <view class="message-content">
      <!-- 文本消息 -->
      <text-message
        v-if="message.type === 'text'"
        :content="message.content"
        :self="message.from === 'self'"
        @copy="handleCopy"
      />

      <!-- 图片消息 -->
      <image-message
        v-else-if="message.type === 'image'"
        :src="message.content"
        :width="message.ext?.width"
        :height="message.ext?.height"
        :self="message.from === 'self'"
        @preview="handlePreviewImage"
      />

      <!-- 语音消息 -->
      <voice-message
        v-else-if="message.type === 'voice'"
        :src="message.content"
        :duration="message.ext?.duration || 0"
        :self="message.from === 'self'"
        @play="handlePlayVoice"
      />

      <!-- 视频消息 -->
      <video-message
        v-else-if="message.type === 'video'"
        :src="message.content"
        :poster="message.ext?.thumbnail"
        :width="message.ext?.width"
        :height="message.ext?.height"
        :duration="message.ext?.duration"
      />

      <!-- 位置消息 -->
      <location-message
        v-else-if="message.type === 'location'"
        :name="message.content.name"
        :address="message.content.address"
        :latitude="message.content.latitude"
        :longitude="message.content.longitude"
        :imageUrl="message.content.image"
        @open="handleOpenLocation"
      />

      <!-- 系统消息 -->
      <view v-else-if="message.type === 'system'" class="system-message">
        <text>{{ message.content }}</text>
      </view>

      <!-- 未知消息类型 -->
      <!-- <view v-else class="unknown-message">
        <text>{{ message.content }}</text>
      </view> -->

      <!-- 消息状态 -->
      <!-- <view v-if="message.from === 'self'" class="message-status">
        <text v-if="message.status === 'sending'" class="status-sending">发送中...</text>
        <view v-else-if="message.status === 'failed'" class="status-failed" @click="handleResend">
          <uni-icons type="warn" size="16" color="#ff5151" />
        </view>
        <text v-else-if="message.status === 'sent' && !message.isRead" class="status-sent">
          已发送
        </text>
        <text v-else-if="message.status === 'sent' && message.isRead" class="status-read">
          已读
        </text>
      </view> -->
    </view>

    <!-- 当前用户头像容器 -->
    <!-- <view class="avatar-container">
      <image v-if="message.from === 'self'" class="avatar" :src="selfAvatar" mode="aspectFill" />
    </view> -->
  </view>
</template>

<script setup lang="ts">
import TextMessage from "./type/TextMessage.vue";
import ImageMessage from "./type/ImageMessage.vue";
import VoiceMessage from "./type/VoiceMessage.vue";
import VideoMessage from "./type/VideoMessage.vue";
import LocationMessage from "./type/LocationMessage.vue";
import type { ChatMsg } from "@/types/chatMsg";

// 组件属性
const props = defineProps<{
  message: ChatMsg.Message;
  otherAvatar: string;
  selfAvatar: string;
}>();

console.log(props.message);

// 组件事件
const emit = defineEmits<{
  (e: "previewImage", url: string): void;
  (e: "playVoice", message: ChatMsg.VoiceMessage): void;
  (e: "openLocation", location: ChatMsg.LocationMessage["content"]): void;
  (e: "resend", message: ChatMsg.Message): void;
  (e: "copy", content: string): void;
}>();

const self = computed(() => {
  return props.message.from === "self";
});

const avatarUrl = computed(() => {
  return props.message.from === "self" ? props.selfAvatar : props.otherAvatar;
});

// 处理图片预览
const handlePreviewImage = (url: string) => {
  emit("previewImage", url);
};

// 处理播放语音
const handlePlayVoice = () => {
  if (props.message.type === "voice") {
    emit("playVoice", props.message);
  }
};

// 处理打开位置
const handleOpenLocation = () => {
  if (props.message.type === "location") {
    emit("openLocation", props.message.content);
  }
};

// 处理重新发送
const handleResend = () => {
  emit("resend", props.message);
};

// 处理复制文本
const handleCopy = (content: string) => {
  emit("copy", content);
};
</script>

<style lang="scss" scoped>
.bubble-box {
  display: flex;
  align-items: flex-start;
  padding: 0 24rpx;
  margin-bottom: 32rpx;

  .avatar-container {
    flex: 0 0 88rpx; /* 固定宽度 */
    width: 88rpx;
    height: 88rpx;
  }

  .avatar {
    display: block;
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  }

  .message-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    max-width: calc(100% - 200rpx);
    padding: 0 20rpx;
  }

  .message-status {
    position: absolute;
    bottom: -40rpx;
    font-size: 24rpx;

    .status-sending {
      color: #999999;
    }

    .status-failed {
      color: #ff5151;
    }

    .status-sent {
      color: #999999;
    }

    .status-read {
      color: #1e80ff;
    }
  }
}

.self {
  flex-direction: row-reverse;
  .message-content {
    align-items: flex-end;
  }
}

.system-message {
  padding: 8rpx 20rpx;
  margin: 0 auto;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10rpx;
}

.unknown-message {
  padding: 16rpx;
  font-size: 28rpx;
  color: #666666;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}
</style>
