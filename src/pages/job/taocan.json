{
  "enterprise_cooperation_plans": {
    "version": "4.0",
    "last_updated": "2025-07-03",
    "core_discount_strategy": {
      "quarterly_discount": 0.15,
      "semi_annual_discount": 0.25,
      "annual_discount": 0.35,
      "county_additional_discount": 0.1,
      "payment_terms": "预付制度，到期自动转为按需发布"
    },

    // ========== 星火计划（中小企业适用） ==========
    "spark_plan": {
      "target": "10-50人中小企业",
      "core_features": [
        "5个并发职位",
        "每日10次免费刷新",
        "基础人才匹配（5人/月）",
        "企业认证标识"
      ],
      "pricing_tiers": [
        {
          "duration": "monthly",
          "standard_price": 99.0,
          "quarterly_price": 253.0, // 99*3*0.85
          "semi_annual_price": 445.0, // 99*6*0.75
          "annual_price": 772.0, // 99*12*0.65
          "county_price": {
            "monthly": 79.0,
            "quarterly": 202.0,
            "semi_annual": 356.0,
            "annual": 618.0
          },
          "daily_cost": {
            "standard": 3.3,
            "annual": 2.1
          },
          "bonus_offer": {
            "quarterly": "急聘加速包1个",
            "semi_annual": "急聘加速包3个",
            "annual": "年度招聘数据报告"
          }
        }
      ],
      "cost_advantage": "年付比月付节省35%，县域再省10%"
    },

    // ========== 燎原计划（成长型企业适用） ==========
    "prairie_fire_plan": {
      "target": "50-200人成长企业",
      "core_features": [
        "无限职位发布",
        "每日20次免费刷新",
        "高级人才匹配（20人/月）",
        "人才库基础版",
        "急聘加速包（2个/月）",
        "API对接权限"
      ],
      "pricing_tiers": [
        {
          "duration": "monthly",
          "standard_price": 199.0,
          "quarterly_price": 508.0, // 199*3*0.85
          "semi_annual_price": 895.0, // 199*6*0.75
          "annual_price": 1553.0, // 199*12*0.65
          "county_price": {
            "monthly": 159.0,
            "quarterly": 406.0,
            "semi_annual": 716.0,
            "annual": 1242.0
          },
          "daily_cost": {
            "standard": 6.6,
            "annual": 4.3
          },
          "bonus_offer": {
            "quarterly": "人才匹配包+5人",
            "semi_annual": "专属招聘页面（1个月）",
            "annual": "政府补贴申领服务"
          }
        }
      ],
      "cost_advantage": "年付日均成本低至月付的65%"
    },

    // ========== 鲲鹏计划（大型企业定制） ==========
    "roc_plan": {
      "target": "200人以上大型企业",
      "core_features": [
        "专属客户经理",
        "定制招聘门户",
        "批量管理工具",
        "人才测评系统",
        "政府补贴申领协助",
        "竞品分析报告"
      ],
      "pricing_tiers": [
        {
          "duration": "monthly",
          "standard_price": 499.0,
          "quarterly_price": 1274.0, // 499*3*0.85
          "semi_annual_price": 2245.0, // 499*6*0.75
          "annual_price": 3892.0, // 499*12*0.65
          "county_price": {
            "monthly": 399.0,
            "quarterly": 1020.0,
            "semi_annual": 1796.0,
            "annual": 3114.0
          },
          "custom_enterprise_discount": {
            "min_employees": 300,
            "max_discount": 0.5,
            "condition": "签订年度框架协议"
          },
          "bonus_offer": {
            "quarterly": "员工培训1次",
            "semi_annual": "定制人才测评系统",
            "annual": "专属招聘峰会席位"
          }
        }
      ],
      "value_added_services": [
        {
          "service": "政府补贴申领",
          "estimated_value": "3000-5000元/岗位",
          "success_rate": "县域企业85%通过率"
        },
        {
          "service": "批量入职管理",
          "time_saving": "HR效率提升70%"
        }
      ]
    },

    // ========== 跨套餐升级优惠 ==========
    "upgrade_path": {
      "spark_to_prairie": {
        "discount": "剩余价值折抵+15%额外优惠",
        "example": "星火年付剩余6个月 → 燎原年付仅需补差价×0.85"
      },
      "county_special": {
        "annual_offer": "买三年送一年",
        "condition": "县域企业首次签约三年期"
      }
    },

    // ========== 技术实现规范 ==========
    "tech_specification": {
      "ui_components": [
        {
          "name": "PlanDurationSelector",
          "props": {
            "plans": ["月付", "季付", "半年付", "年付"],
            "discountBadgeColor": "orange-500",
            "recommendedTag": "最划算"
          }
        },
        {
          "name": "CountyPriceDisplay",
          "logic": "自动检测IP/营业执照显示县域价"
        }
      ],
      "payment_integration": {
        "wx_payment": true,
        "bank_transfer": true,
        "voucher_system": "企业套餐专用代金券"
      },
      "entitlement_management": {
        "api_endpoint": "/api/entitlements",
        "cache_strategy": "localStorage+定时刷新"
      }
    }
  }
}
