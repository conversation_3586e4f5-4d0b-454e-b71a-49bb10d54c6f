<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <CustomNavBar
      :title="navTitle"
      :background="navBgColor"
      :title-color="navTitleColor"
      :show-share="true"
      @back="goBack"
      @share="handleShare"
    />

    <!-- 房源图片轮播 -->
    <view class="swiper-container">
      <swiper class="house-swiper" indicator-dots circular autoplay indicator-active-color="rgba(255, 255, 255, 0.8)">
        <swiper-item v-for="(image, index) in houseImages" :key="index">
          <image :src="image" mode="aspectFill" class="swiper-image" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 房源基本信息 -->
    <view class="content-wrapper">
      <CustomCard class="info-card">
        <view class="house-header">
          <view class="title-section">
            <text class="house-title">{{ houseDetail.title }}</text>
            <view class="house-tags">
              <text v-if="houseDetail.hasVR" class="vr-tag">VR看房</text>
              <text class="status-tag" :class="getStatusClass(houseDetail.status)">
                {{ houseDetail.status }}
              </text>
            </view>
          </view>
          <view class="favorite-btn" @tap="toggleFavorite">
            <text class="i-carbon-heart text-24px" :class="{ favorited: isFavorited }" />
          </view>
        </view>

        <view class="price-section">
          <text class="current-price">{{ houseDetail.price }}</text>
          <text v-if="houseDetail.priceRange" class="original-price">
            参考总价 {{ houseDetail.priceRange }}
          </text>
        </view>

        <view v-if="houseDetail.specialOffer" class="special-offer">
          <text class="offer-text">{{ houseDetail.specialOffer }}</text>
        </view>

        <view class="location-info">
          <text class="i-carbon-location location-icon" />
          <text class="location-text">{{ houseDetail.location }}</text>
        </view>

        <view class="build-types">
          <text class="types-label">户型：</text>
          <text v-for="(type, index) in houseDetail.buildTypes" :key="type" class="build-type">
            {{ type
            }}<text v-if="index < houseDetail.buildTypes.length - 1">、</text>
          </text>
        </view>

        <view class="tags-section">
          <text v-for="tag in houseDetail.tags" :key="tag" class="tag">{{ tag }}</text>
        </view>
      </CustomCard>

      <!-- 楼盘信息标签页 -->
      <CustomCard padding="0">
        <HouseInfoTabs>
          <template #basic-info>
            <BasicInfoPanel :basicInfo="houseBasicInfo" />
          </template>
          <template #sales-info>
            <SalesInfoPanel :salesInfo="houseSalesInfo" />
          </template>
          <template #community-info>
            <CommunityInfoPanel :communityInfo="houseCommunityInfo" />
          </template>
          <template #license-info>
            <LicenseInfoPanel :licenses="houseLicenses" />
          </template>
        </HouseInfoTabs>
      </CustomCard>

      <!-- 户型信息 -->
      <CustomCard>
        <view class="section-header">
          <text class="section-title">户型信息</text>
          <view class="view-all-btn">
            <text>查看全部</text>
            <text class="i-carbon-arrow-right ml-4rpx" />
          </view>
        </view>
        <view class="layout-filter">
          <view
            v-for="(filter, index) in layoutFilters"
            :key="index"
            class="filter-chip"
            :class="{ active: activeLayoutFilter === index }"
            @tap="switchLayoutFilter(index)"
          >
            <text>{{ filter }}</text>
          </view>
        </view>
        <scroll-view scroll-x class="layout-scroll-view">
          <view class="layout-list-horizontal">
            <CustomCard
              v-for="layout in houseDetail.layouts"
              :key="layout.id"
              class="layout-item-card"
              margin="0 16rpx 16rpx 0"
              padding="0"
              :border-radius="'16rpx'"
            >
              <view class="layout-item-horizontal">
                <view class="layout-image-wrap">
                  <image :src="layout.image" mode="aspectFill" class="layout-image" />
                  <view v-if="layout.isMain" class="main-layout-tag">主力户型</view>
                  <view v-if="layout.hasVR" class="vr-tag-on-image">
                    <text class="i-carbon-video-filled mr-4rpx" />
                    <text>VR带看</text>
                  </view>
                </view>
                <view class="layout-info">
                  <view class="layout-title-row">
                    <text class="layout-type">{{ layout.type }}</text>
                    <text class="layout-status" :class="getStatusClass(layout.status)">{{ layout.status }}</text>
                  </view>
                  <text class="layout-area-direction">
                    建面{{ layout.area }}㎡ / {{ layout.direction }}
                  </text>
                  <text class="layout-price">约{{ layout.price }}/套</text>
                  <view class="layout-actions">
                    <button class="layout-action-btn secondary">户型报告</button>
                    <button class="layout-action-btn primary">咨询首付</button>
                  </view>
                </view>
              </view>
            </CustomCard>
          </view>
        </scroll-view>
      </CustomCard>

      <!-- 周边配套 -->
      <CustomCard>
        <view class="section-header">
          <text class="section-title">周边配套</text>
        </view>
        <view class="facility-tabs">
          <view
            v-for="(facility, index) in houseDetail.facilities"
            :key="facility.type"
            class="facility-tab"
            :class="{ active: activeFacilityTab === index }"
            @tap="switchFacilityTab(index)"
          >
            <text>{{ facility.type }}</text>
          </view>
        </view>
        <view v-if="houseDetail.facilities.length > 0" class="facility-content">
          <view class="facility-list">
            <text
              v-for="item in houseDetail.facilities[activeFacilityTab].items"
              :key="item"
              class="facility-item"
            >
              {{ item }}
            </text>
          </view>
        </view>
      </CustomCard>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-item" @tap="toggleFavorite">
        <text class="i-carbon-favorite text-22px" :class="{ favorited: isFavorited }" />
        <text class="item-text">关注</text>
      </view>
      <view class="actions-group">
        <button class="action-btn secondary" @tap="callPhone">电话咨询</button>
        <button class="action-btn primary" @tap="consultNow">在线咨询</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import CustomNavBar from "@/components/CustomNavBar.vue";
import HouseInfoTabs from "./components/HouseInfoTabs.vue";
import BasicInfoPanel from "./components/BasicInfoPanel.vue";
import SalesInfoPanel from "./components/SalesInfoPanel.vue";
import CommunityInfoPanel from "./components/CommunityInfoPanel.vue";
import LicenseInfoPanel from "./components/LicenseInfoPanel.vue";
import CustomCard from "@/components/common/Card.vue";

const houseId = ref();
const navTitle = ref("");
const navBgColor = ref("transparent");
const navTitleColor = ref("transparent");

onLoad((options: any) => {
  console.log(options);
  houseId.value = options.id;
});

const goBack = () => {
  uni.navigateBack();
};

const handleShare = () => {
  uni.showToast({
    title: "分享功能开发中",
    icon: "none",
  });
};

onPageScroll((e) => {
  const scrollTop = e.scrollTop;
  const opacity = Math.min(scrollTop / 200, 1);
  navBgColor.value = `rgba(255, 255, 255, ${opacity})`;
  if (scrollTop > 200) {
    navTitle.value = "楼盘详情";
    navTitleColor.value = "#000";
  } else {
    navTitle.value = "";
    navTitleColor.value = "transparent";
  }
});

// 收藏状态
const isFavorited = ref(false);

// 户型筛选
const layoutFilters = ["全部(17)", "一居(1)", "二居(1)", "三居(12)"];
const activeLayoutFilter = ref(0);
const switchLayoutFilter = (index: number) => {
  activeLayoutFilter.value = index;
};

// 周边配套筛选
const activeFacilityTab = ref(0);
const switchFacilityTab = (index: number) => {
  activeFacilityTab.value = index;
};

// 基础信息数据
const houseBasicInfo = reactive({
  alias: "丽都壹号·峯境",
  features: "公交直达、水系园林、综合商场、公园",
  price: "78000元/平",
  totalPrice: "1280-1600万/套",
  propertyType: "住宅",
  buildingType: "板楼",
  greenBuilding: "否",
  decoration: "毛坯",
  propertyYears: 70,
  brand: "永同昌集团",
  developer: "北京京投隆德置业有限公司",
  area: "朝阳",
  address: "将台路与驼房营路交叉口向北150米，将府家园北里",
});

// 销售信息数据
const houseSalesInfo = reactive({
  status: "在售",
  buildings: "共1栋",
  layouts: "1/2/4居",
  openingDate: "2020-05-01",
  deliveryDate: "2022-03-01",
  salesAddress: "朝阳区将台路与驼房营路交叉口向北150米",
  businessHours: "09:00~18:00",
  requireDeposit: "否",
});

// 小区概况数据
const houseCommunityInfo = reactive({
  landArea: "81,586m²",
  buildingArea: "237,231m²",
  greenArea: "24,000m²",
  greenRate: "30%",
  plotRatio: "2.43",
  parkingSpaces: "地下482个",
  parkingRatio: "1:0.7",
  buildingCount: "8栋",
  houseCount: "1954户",
  propertyCompany: "北京西国贸大物业",
  propertyFee: "3.3元/m²/月",
  heatingMode: "集中供暖",
  waterSupply: "民水",
});

// 预售许可证数据
const houseLicenses = ref([
  {
    number: "京房售证字(2020)10号",
    issueDate: "2020-03-15",
    authority: "北京市住房和城乡建设委员会",
    scope: "1号楼、2号楼",
    unitCount: "356户",
  },
  {
    number: "京房售证字(2020)22号",
    issueDate: "2020-05-20",
    authority: "北京市住房和城乡建设委员会",
    scope: "3号楼、4号楼",
    unitCount: "420户",
  },
]);

// 房源详情数据
const houseDetail = ref<any>({
  id: "1",
  title: "京投发展森与天成",
  image: "https://picsum.photos/seed/house-1/300/200",
  location: "丰台·新宫/建面55-148㎡/1,2,3,4居",
  price: "78000元/平",
  originalPrice: "80000元/平",
  priceRange: "380-1250万/套",
  specialOffer: "线下优惠可咨询顾问",
  buildTypes: ["1居", "2居", "3居"],
  tags: ["近地铁", "公园", "期房", "大三居"],
  openTime: "2023-10-28",
  attentionCount: 29,
  hasVR: true,
  status: "在售",
  developer: "北京京投隆德置业有限公司",
  propertyType: "住宅",
  propertyYears: 70,
  decoration: "毛坯",
  deliveryTime: "2025年12月",
  salesAddress: "丰台区新宫地铁站附近",
  salesPhone: "************",
  businessHours: "09:00-18:00",
  layouts: [
    {
      id: 1,
      type: "2室2厅1卫",
      area: "91",
      direction: "南",
      price: "890万",
      image: "https://picsum.photos/seed/layout-1/400/300",
      isMain: true,
      hasVR: true,
      status: "在售",
    },
    {
      id: 2,
      type: "2室2厅1卫",
      area: "88",
      direction: "南北",
      price: "840万",
      image: "https://picsum.photos/seed/layout-2/400/300",
      isMain: false,
      hasVR: true,
      status: "在售",
    },
    {
      id: 3,
      type: "3室2厅2卫",
      area: "125",
      direction: "东西",
      price: "1100万",
      image: "https://picsum.photos/seed/layout-3/400/300",
      isMain: false,
      hasVR: false,
      status: "待售",
    },
    {
      id: 4,
      type: "4室2厅2卫",
      area: "148",
      direction: "南",
      price: "1350万",
      image: "https://picsum.photos/seed/layout-4/400/300",
      isMain: true,
      hasVR: true,
      status: "在售",
    },
  ],
  facilities: [
    {
      type: "交通",
      items: ["地铁1号线", "公交站", "高速入口"],
    },
    {
      type: "教育",
      items: ["石景山实验小学", "九中分校", "北京理工大学"],
    },
    {
      type: "医疗",
      items: ["石景山医院", "社区卫生服务中心"],
    },
    {
      type: "商业",
      items: ["万达广场", "华联超市", "银行网点"],
    },
  ],
});

// 房源图片
const houseImages = ref([
  "https://picsum.photos/seed/house-detail-1/400/300",
  "https://picsum.photos/seed/house-detail-2/400/300",
  "https://picsum.photos/seed/house-detail-3/400/300",
  "https://picsum.photos/seed/house-detail-4/400/300",
]);

// 方法
const getStatusClass = (status: string) => {
  const statusMap: { [key: string]: string } = {
    在售: "on-sale",
    待售: "pending",
    售罄: "sold-out",
  };
  return statusMap[status] || "on-sale";
};

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value;
  uni.showToast({
    title: isFavorited.value ? "已关注" : "已取消关注",
    icon: "none",
  });
};

const consultNow = () => {
  uni.showToast({
    title: "咨询功能开发中",
    icon: "none",
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: houseDetail.value.salesPhone,
  });
};

// 根据ID加载房源详情
const loadHouseDetail = (id: string) => {
  // 这里应该调用API获取房源详情
  console.log("加载房源详情:", id);
  // 模拟数据已在上面定义
};

onMounted(() => {
  loadHouseDetail(houseId.value);
});
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--bg-page);
  min-height: 100vh;
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
}

.swiper-container {
  width: 100%;
  height: 500rpx;
}

.house-swiper {
  width: 100%;
  height: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.content-wrapper {
  padding: 0 32rpx;
  margin-top: -32rpx;
  position: relative;
  z-index: 10;
}

.info-card {
  margin-bottom: 24rpx;
}

.house-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.title-section {
  flex: 1;
  margin-right: 24rpx;
}

.house-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
}

.house-tags {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.vr-tag {
  background-color: var(--bg-primary-light);
  color: var(--primary);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-tag {
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
}

.status-tag.on-sale {
  background-color: var(--primary);
}
.status-tag.pending {
  background-color: var(--warning);
}
.status-tag.sold-out {
  background-color: var(--text-secondary);
}

.favorite-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border-radius: 50%;
  color: var(--text-secondary);
  .favorited {
    color: var(--error);
  }
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.current-price {
  font-size: 44rpx;
  font-weight: 700;
  color: var(--error);
}

.original-price {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.special-offer {
  margin-bottom: 24rpx;
}

.offer-text {
  color: var(--error);
  font-size: 26rpx;
  background-color: var(--bg-danger-light);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.location-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 24rpx 0;
  border-top: 1rpx solid var(--border-color-light);
  border-bottom: 1rpx solid var(--border-color-light);
}

.location-icon {
  color: var(--text-secondary);
  margin-right: 12rpx;
  margin-top: 6rpx;
}

.location-text {
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.5;
  flex: 1;
}

.build-types {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  flex-wrap: wrap;
}

.types-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.build-type {
  font-size: 28rpx;
  color: var(--text-primary);
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  background-color: var(--bg-tag);
  color: var(--text-secondary);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.section-header {
  margin-bottom: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.view-all-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: var(--text-secondary);
}

.layout-filter {
  display: flex;
  gap: 16rpx;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 32rpx;
  padding-bottom: 8rpx;
}

.filter-chip {
  padding: 12rpx 32rpx;
  background-color: var(--bg-light);
  color: var(--text-secondary);
  border-radius: 99rpx;
  font-size: 26rpx;
  transition: all 0.3s;
}

.filter-chip.active {
  background-color: var(--bg-primary-light);
  color: var(--primary);
  font-weight: 500;
}

.layout-scroll-view {
  width: 100%;
  white-space: nowrap;
  margin: 0 -32rpx;
  padding: 0 32rpx;
}

.layout-list-horizontal {
  display: inline-flex;
  padding-bottom: 8rpx;
}

.layout-item-card {
  display: inline-block;
  width: 520rpx;
}

.layout-item-horizontal {
  .layout-image-wrap {
    width: 100%;
    height: 320rpx;
    position: relative;
    overflow: hidden;
    border-radius: 16rpx 16rpx 0 0;

    .layout-image {
      width: 100%;
      height: 100%;
    }
    .main-layout-tag {
      position: absolute;
      top: 0;
      left: 0;
      background-color: var(--primary);
      color: white;
      padding: 6rpx 16rpx;
      font-size: 22rpx;
      border-bottom-right-radius: 16rpx;
    }
    .vr-tag-on-image {
      position: absolute;
      bottom: 16rpx;
      left: 16rpx;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 99rpx;
      font-size: 24rpx;
      display: flex;
      align-items: center;
      backdrop-filter: blur(4px);
    }
  }

  .layout-info {
    padding: 24rpx;
    .layout-title-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;
      .layout-type {
        font-size: 32rpx;
        font-weight: 600;
        color: var(--text-primary);
      }
      .layout-status {
        font-size: 22rpx;
        color: #fff;
        padding: 6rpx 12rpx;
        border-radius: 8rpx;
      }
    }
    .layout-area-direction {
      font-size: 26rpx;
      color: var(--text-secondary);
      margin-bottom: 16rpx;
      display: block;
    }
    .layout-price {
      font-size: 32rpx;
      font-weight: 700;
      color: var(--error);
      margin-bottom: 24rpx;
      display: block;
    }
    .layout-actions {
      display: flex;
      gap: 16rpx;
      .layout-action-btn {
        flex: 1;
        height: 72rpx;
        line-height: 72rpx;
        border-radius: 36rpx;
        font-size: 28rpx;
        padding: 0;
        margin: 0;
        &.secondary {
          background-color: var(--bg-primary-light);
          color: var(--primary);
          border: none;
        }
        &.primary {
          background-color: var(--primary);
          color: white;
        }
      }
    }
  }
}

.facility-tabs {
  display: flex;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid var(--border-color-light);
}

.facility-tab {
  padding-bottom: 24rpx;
  margin-right: 48rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  position: relative;
  transition: all 0.3s;
}

.facility-tab.active {
  color: var(--primary);
  font-weight: 600;
  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 6rpx;
    background-color: var(--primary);
    border-radius: 3rpx;
  }
}

.facility-content {
  padding: 16rpx 0;
}

.facility-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.facility-item {
  background-color: var(--bg-light);
  color: var(--text-secondary);
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid var(--border-color-light);
  display: flex;
  align-items: center;
  gap: 24rpx;
  z-index: 100;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  width: 120rpx;
  font-size: 22rpx;
  color: var(--text-secondary);

  .favorited {
    color: var(--error);
  }
}

.actions-group {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 44rpx;
  padding: 0;
  margin: 0;
  &.secondary {
    background-color: var(--bg-primary-light);
    color: var(--primary);
  }
  &.primary {
    background: var(--primary);
    color: #fff;
  }
}
</style>
