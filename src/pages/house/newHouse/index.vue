<template>
  <view class="container">
    <!-- 渐变背景 -->
    <view class="gradient-bg" />

    <!-- 粘性头部：搜索和筛选 -->
    <view class="sticky-header">
      <!-- 搜索栏 -->
      <view class="search-section">
        <view class="search-bar" @tap="handleSearch">
          <text class="i-carbon-search text-20px text-gray-400" />
          <text class="search-placeholder">搜索楼盘名称、位置</text>
        </view>
      </view>

      <!-- 筛选组件 -->
      <view class="filter-container">
        <FilterPanel
          @filter-change="handleFilterChange"
          @open-filter="openFilter"
          @close-filter="closeFilter"
        />
      </view>
    </view>

    <!-- 内容区域 - 使用z-paging组件 -->
    <z-paging
      ref="pagingRef"
      v-model="houseList"
      :safe-area-inset-bottom="true"
      :top="stickyHeaderHeight"
      empty-view-text="暂无房源数据"
      @query="queryHouseList"
    >
      <view class="house-list-container">
        <view
          v-for="house in houseList"
          :key="house.id"
          class="house-card-container"
          @tap="navigateToDetail(house.id)"
        >
          <CustomCard padding="0" class="house-card">
            <!-- 房源图片 -->
            <view class="house-image-wrapper">
              <image :src="house.image" mode="aspectFill" class="house-image" :lazy-load="true" />
              <!-- 状态标签 -->
              <view
                v-if="house.status"
                class="status-badge"
                :class="getStatusClass(house.status)"
              >
                {{ house.status }}
              </view>
            </view>

            <!-- 房源信息 -->
            <view class="house-content">
              <!-- 标题和房源类型 -->
              <view class="house-header">
                <text class="house-title">{{ house.title }}</text>
                <view v-if="house.propertyType" class="property-type-badge">
                  {{ house.propertyType }}
                </view>
              </view>

              <!-- 价格区域 -->
              <view class="price-area">
                <text class="house-price" :class="{ 'price-pending': house.price === '价格待定' }">
                  {{ house.price }}
                </text>
                <view v-if="house.specialOffer" class="special-tag">
                  {{ house.specialOffer }}
                </view>
              </view>

              <!-- 位置信息 -->
              <text class="house-location">{{ house.location }}</text>

              <!-- 标签区域 -->
              <view v-if="house.tags && house.tags.length" class="tags-container">
                <text v-for="tag in house.tags" :key="tag" class="info-tag">
                  {{ tag }}
                </text>
              </view>

              <!-- 关注信息 -->
              <view v-if="house.attentionCount" class="attention-text">
                <text class="i-carbon-favorite text-red-500" />
                近期有{{ house.attentionCount }}人关注
              </view>
            </view>
          </CustomCard>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import type { NewHouseItem } from "@/types/house";
import FilterPanel from "./components/FilterPanel.vue";
import CustomCard from "@/components/common/Card.vue";

// z-paging引用
const pagingRef = ref(null);
const stickyHeaderHeight = ref(100); // 默认高度

// 状态栏高度
const statusBarHeight = ref(0);

// 当前页码
const showSearchBar = ref(true);

// 响应式数据
const currentLocation = ref("北京");
const showFilterModal = ref(false);

// 筛选条件
const currentFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  features: [] as string[],
  developer: "",
  decoration: "",
});

// 选中的筛选条件
const selectedFilters = reactive({
  price: "",
  houseType: "",
  features: [] as string[],
});

// 保存原始房源列表数据
const originalHouseList = [
  {
    id: "1",
    image: "https://picsum.photos/seed/house-1/300/200",
    title: "元垄石景山",
    location: "石景山·石景山其它/建面120-196㎡/3,4居",
    price: "75000元/平",
    specialOffer: "有15套特价房",
    tags: ["近地铁", "综合商场", "公园"],
    attentionCount: 30,
    status: "在售",
    priceRange: "总价950-1560万",
    propertyType: "住宅",
  },
  {
    id: "2",
    image: "https://picsum.photos/seed/house-2/300/200",
    title: "招商云璟",
    location: "通州·梨园/建面79-128㎡/3,4居",
    price: "60000元/平",
    specialOffer: "有9套特价房",
    tags: ["近地铁", "医疗配套", "期房"],
    attentionCount: 33,
    status: "在售",
    priceRange: "总价470-830万",
    propertyType: "住宅",
  },
  {
    id: "smds",
    image: "https://picsum.photos/seed/house-smds/300/200",
    title: "世茂维拉左右间",
    location: "房山·长阳",
    price: "价格待定",
    specialOffer: "",
    tags: ["近地铁", "综合商场", "小型社区"],
    attentionCount: 10,
    status: "待售",
    priceRange: "",
    propertyType: "写字楼",
  },
  {
    id: "cayh",
    image: "https://picsum.photos/seed/house-cayh/300/200",
    title: "长安运河",
    location: "通州·万达",
    price: "价格待定",
    specialOffer: "",
    tags: ["近地铁", "综合商场", "三甲医院"],
    attentionCount: 5,
    status: "待售",
    priceRange: "",
    propertyType: "商业类",
  },
  {
    id: "3",
    image: "https://picsum.photos/seed/house-3/300/200",
    title: "京投发展森与天成",
    location: "丰台·新宫/建面55-148㎡/1,2,3,4居",
    price: "78000元/平",
    specialOffer: "",
    tags: ["近地铁", "公园", "期房"],
    attentionCount: 0,
    status: "在售",
    priceRange: "总价430-1150万",
    propertyType: "住宅",
  },
  {
    id: "4",
    image: "https://picsum.photos/seed/house-4/300/200",
    title: "清樾府",
    location: "昌平·沙河/建面86-143㎡/3,4居",
    price: "46000元/平",
    specialOffer: "",
    tags: ["期房", "小三居", "低密居所"],
    attentionCount: 39,
    status: "在售",
    priceRange: "总价410-680万",
    propertyType: "住宅",
  },
];

// 热门楼盘数据
const hotHouses = ref([
  {
    id: "h1",
    image: "https://picsum.photos/seed/hot-1/300/200",
    title: "首开龙湖·云著",
    price: "62000元/平",
    tags: ["品牌开发商", "低密度", "热销楼盘"],
  },
  {
    id: "h2",
    image: "https://picsum.photos/seed/hot-2/300/200",
    title: "华润西山金茂府",
    price: "85000元/平",
    tags: ["品牌开发商", "地铁房", "公园"],
  },
  {
    id: "h3",
    image: "https://picsum.photos/seed/hot-3/300/200",
    title: "万科蓝山",
    price: "58000元/平",
    tags: ["品牌开发商", "低总价", "精装修"],
  },
  {
    id: "h4",
    image: "https://picsum.photos/seed/hot-4/300/200",
    title: "朗润园",
    price: "53000元/平",
    tags: ["低总价", "地铁房", "配套齐全"],
  },
]);

// 房源列表数据
const houseList = ref<NewHouseItem[]>([]);

// 方法
const navigateBack = () => {
  uni.navigateBack();
};

const handleSearch = () => {
  uni.showToast({
    title: "搜索功能开发中",
    icon: "none",
  });
};

const selectLocation = () => {
  uni.showActionSheet({
    itemList: ["北京", "上海", "广州", "深圳", "杭州"],
    success: (res) => {
      const locations = ["北京", "上海", "广州", "深圳", "杭州"];
      currentLocation.value = locations[res.tapIndex];
      // 切换城市后重新加载数据
      if (pagingRef.value) {
        pagingRef.value.reload();
      }
    },
  });
};

// 查看更多热门楼盘
const viewMoreHotHouse = () => {
  uni.showToast({
    title: "查看更多热门楼盘",
    icon: "none",
  });
};

// 查看更多开盘日历
const viewMoreCalendar = () => {
  uni.showToast({
    title: "查看更多开盘日历",
    icon: "none",
  });
};

// 预约看房
const appointHouse = (id: string) => {
  uni.showToast({
    title: "预约看房功能开发中",
    icon: "none",
  });
};

const getStatusClass = (status: string) => {
  switch (status) {
    case "在售":
      return "status-selling";
    case "待售":
      return "status-pending-sale";
    case "热销":
      return "status-hot";
    case "即将开盘":
      return "status-coming";
    case "售罄":
      return "status-sold";
    case "二手房":
      return "status-second";
    default:
      return "status-default";
  }
};

const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/newHouse/detail?id=${id}`,
  });
};

// z-paging查询数据方法
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里模拟分页数据请求
  pagingRef.value.complete(originalHouseList);
};

// 打开筛选框
const openFilter = () => {
  showSearchBar.value = false;
};

// 关闭筛选框
const closeFilter = () => {
  showSearchBar.value = true;
};

// 处理筛选变化
const handleFilterChange = (filters: any) => {
  showSearchBar.value = true;
  Object.assign(currentFilters, filters);
  console.log("筛选条件已更新:", currentFilters);

  // 重新加载数据
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

onMounted(() => {
  // 获取状态栏高度
  const { statusBarHeight: height } = uni.getSystemInfoSync();
  statusBarHeight.value = height || 0;

  // 获取粘性头部高度
  nextTick(() => {
    const query = uni.createSelectorQuery();
    query
      .select(".sticky-header")
      .boundingClientRect((data) => {
        const rect = data as UniApp.NodeInfo;
        if (rect && rect.height) {
          stickyHeaderHeight.value = rect.height;
        }
      })
      .exec();
  });
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-page);
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 500rpx;
  background-image: linear-gradient(
      180deg,
      rgba(102, 126, 234, 0.2) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(128, 128, 255, 0.25),
      transparent 35%
    ),
    radial-gradient(
      circle at 20% 70%,
      rgba(173, 216, 230, 0.3),
      transparent 35%
    );
  background-color: var(--bg-primary-light);
  filter: blur(50rpx);
  transform: scale(1.5);
  z-index: 0;
  opacity: 0.8;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: var(--bg-page);
  transition: box-shadow 0.3s;
}

:deep(.zp-scroll-view) {
  background: transparent !important;
}

/* 搜索栏样式 */
.search-section {
  padding: 24rpx 32rpx;
  background-color: transparent;
}

.search-bar {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding: 0 32rpx;
  background-color: var(--bg-card);
  border-radius: 40rpx;
  gap: 16rpx;
  box-shadow: var(--shadow-sm);
}

.search-placeholder {
  color: var(--text-grey);
  font-size: 28rpx;
}

/* 筛选栏样式 */
.filter-container {
  background: var(--bg-page);
  border-bottom: 1rpx solid var(--border-color-light);
}

.house-list-container {
  padding: 0 32rpx;
  padding-bottom: 24rpx;
}

.house-card-container {
  margin-top: 24rpx;
}

.house-card {
  overflow: hidden;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
  &:active {
    transform: scale(0.98);
    box-shadow: var(--shadow-lg);
  }
}

.house-image-wrapper {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
}

.house-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.status-badge {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 99rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.status-selling {
  background-color: rgba(102, 126, 234, 0.8);
}

.status-pending-sale {
  background-color: rgba(240, 147, 251, 0.8);
}

.status-hot {
  background-color: rgba(255, 99, 71, 0.8);
}

.status-coming {
  background-color: rgba(60, 179, 113, 0.8);
}

.status-sold {
  background-color: rgba(168, 168, 168, 0.8);
}

.house-content {
  padding: 24rpx;
  background-color: var(--bg-card);
  border-radius: 0 0 16rpx 16rpx;
}

.house-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.house-title {
  font-size: 34rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
}

.property-type-badge {
  padding: 6rpx 12rpx;
  background: var(--bg-primary-light);
  border-radius: 8rpx;
  font-size: 22rpx;
  color: var(--primary);
  font-weight: 500;
  white-space: nowrap;
}

.price-area {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
  gap: 12rpx;
}

.house-price {
  font-size: 38rpx;
  font-weight: 700;
  color: var(--text-red);
}

.price-pending {
  color: var(--text-orange);
  font-size: 32rpx;
}

.special-tag {
  padding: 4rpx 12rpx;
  background: var(--bg-danger-light);
  border-radius: 6rpx;
  font-size: 20rpx;
  color: var(--text-red);
  font-weight: 500;
}

.house-location {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.info-tag {
  padding: 6rpx 12rpx;
  background: var(--bg-tag);
  border-radius: 8rpx;
  font-size: 22rpx;
  color: var(--text-secondary);
}

.attention-text {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: var(--text-secondary);
}
</style>