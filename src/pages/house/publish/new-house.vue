<template>
  <view class="new-house-publish-page">
    <uni-nav-bar
      :border="false"
      fixed
      background-color="#ffffff"
      :status-bar="true"
      title="发布新房"
      left-icon="back"
      @clickLeft="goBack"
    />

    <view class="content">
      <view class="guide-section">
        <text class="guide-title">发布新房信息</text>
        <text class="guide-desc"
          >楼盘、户型、价格等信息详细填写，助力快速销售</text
        >
      </view>

      <view class="form-content">
        <tui-form ref="formRef" :model="formData" :rules="formRules">
          <view class="card">
            <view class="section-title">楼盘基本信息</view>

            <tui-form-item
              prop="projectName"
              label="楼盘名称"
              labelSize="30rpx"
              :labelColor="getLabelColor('projectName')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('projectName')"
            >
              <tui-input
                v-model="formData.projectName"
                placeholder="请输入楼盘名称"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('projectName')"
                @blur="handleBlur('projectName')"
              />
            </tui-form-item>

            <tui-form-item
              prop="address"
              label="项目地址"
              labelSize="30rpx"
              :labelColor="getLabelColor('address')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('address')"
            >
              <tui-input
                v-model="formData.address"
                placeholder="请输入项目详细地址"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('address')"
                @blur="handleBlur('address')"
              />
            </tui-form-item>

            <tui-form-item
              prop="developer"
              label="开发商"
              labelSize="30rpx"
              :labelColor="getLabelColor('developer')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('developer')"
            >
              <tui-input
                v-model="formData.developer"
                placeholder="请输入开发商名称"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('developer')"
                @blur="handleBlur('developer')"
              />
            </tui-form-item>

            <tui-form-item
              prop="propertyCompany"
              label="物业公司"
              labelSize="30rpx"
              :labelColor="getLabelColor('propertyCompany')"
              @click="handleFormItemClick('propertyCompany')"
            >
              <tui-input
                v-model="formData.propertyCompany"
                placeholder="请输入物业公司名称"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('propertyCompany')"
                @blur="handleBlur('propertyCompany')"
              />
            </tui-form-item>

            <tui-form-item
              prop="propertyFee"
              label="物业费"
              labelSize="30rpx"
              :labelColor="getLabelColor('propertyFee')"
              @click="handleFormItemClick('propertyFee')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.propertyFee"
                  placeholder="请输入物业费"
                  type="digit"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('propertyFee')"
                  @blur="handleBlur('propertyFee')"
                />
              </view>
              <template #right>
                <text>元/㎡·月</text>
              </template>
            </tui-form-item>

            <tui-form-item
              prop="buildYear"
              label="建设年代"
              labelSize="30rpx"
              :labelColor="getLabelColor('buildYear')"
              arrow
            >
              <picker
                mode="selector"
                :range="BUILD_YEAR_OPTIONS"
                :range-key="'label'"
                :value="buildYearPickerValue"
                @change="onBuildYearChange"
              >
                <tui-input
                  v-model="formData.buildYear"
                  placeholder="请选择"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>

            <tui-form-item
              prop="propertyYears"
              label="产权年限"
              labelSize="30rpx"
              :labelColor="getLabelColor('propertyYears')"
              arrow
            >
              <picker
                mode="selector"
                :range="PROPERTY_YEARS_OPTIONS"
                :range-key="'label'"
                :value="propertyYearsPickerValue"
                @change="onPropertyYearsChange"
              >
                <tui-input
                  v-model="formData.propertyYears"
                  placeholder="请选择"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>

            <tui-form-item
              prop="status"
              label="销售状态"
              labelSize="30rpx"
              :labelColor="getLabelColor('status')"
              arrow
              :bottomBorder="false"
            >
              <picker
                mode="selector"
                :range="SALES_STATUS_OPTIONS"
                :range-key="'label'"
                :value="statusPickerValue"
                @change="onStatusChange"
              >
                <tui-input
                  v-model="formData.status"
                  placeholder="请选择"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">价格信息</view>

            <tui-form-item
              prop="averagePrice"
              label="参考均价"
              labelSize="30rpx"
              :labelColor="getLabelColor('averagePrice')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('averagePrice')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.averagePrice"
                  placeholder="请输入均价"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('averagePrice')"
                  @blur="handleBlur('averagePrice')"
                />
              </view>
              <template #right>
                <text>元/㎡</text>
              </template>
            </tui-form-item>
            <tui-form-item
              prop="priceRange"
              label="参考总价"
              labelSize="30rpx"
              :labelColor="
                getLabelColor('priceRangeMin') || getLabelColor('priceRangeMax')
              "
              :bottomBorder="false"
              @click="handleFormItemClick('priceRangeMin')"
            >
              <view class="price-range-inputs">
                <tui-input
                  v-model="formData.priceRangeMin"
                  placeholder="最低价"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  textAlign="center"
                  @input="handleInput('priceRangeMin')"
                  @blur="handleBlur('priceRangeMin')"
                />
                <text class="price-range-separator">至</text>
                <tui-input
                  v-model="formData.priceRangeMax"
                  placeholder="最高价"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  textAlign="center"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('priceRangeMax')"
                  @blur="handleBlur('priceRangeMax')"
                />
              </view>
              <template #right>
                <text>万/套</text>
              </template>
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">户型信息</view>

            <tui-form-item
              prop="houseTypes"
              label="主力户型"
              labelSize="30rpx"
              :labelColor="getLabelColor('houseTypes')"
              arrow
              @click="openHouseTypeModal()"
            >
              <tui-input
                v-model="formData.houseTypes"
                placeholder="请添加户型信息"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                :disabled="true"
              />
            </tui-form-item>

            <!-- 户型详情列表 -->
            <view
              v-if="formData.houseTypeDetails.length > 0"
              class="house-type-list"
            >
              <view class="house-type-list-title">已添加户型</view>
              <view
                v-for="(houseType, index) in formData.houseTypeDetails"
                :key="houseType.id"
                class="house-type-item"
              >
                <view class="house-type-info">
                  <view class="house-type-name">{{ houseType.name }}</view>
                  <view class="house-type-area">{{ houseType.area }}㎡</view>
                  <view class="house-type-images"
                    >{{ houseType.images.length }}张户型图</view
                  >
                </view>
                <view class="house-type-actions">
                  <text
                    class="action-btn edit"
                    @tap="openHouseTypeModal(houseType)"
                    >编辑</text
                  >
                  <text class="action-btn delete" @tap="deleteHouseType(index)"
                    >删除</text
                  >
                </view>
              </view>
            </view>

            <tui-form-item
              prop="buildingType"
              label="建筑类型"
              labelSize="30rpx"
              :labelColor="getLabelColor('buildingType')"
              arrow
            >
              <picker
                mode="selector"
                :range="BUILDING_TYPE_OPTIONS"
                :range-key="'label'"
                :value="buildingTypePickerValue"
                @change="onBuildingTypeChange"
              >
                <tui-input
                  v-model="formData.buildingType"
                  placeholder="请选择"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>

            <tui-form-item
              prop="decoration"
              label="装修状况"
              labelSize="30rpx"
              :labelColor="getLabelColor('decoration')"
              arrow
              :bottomBorder="false"
              @click="selectDecoration"
            >
              <tui-input
                v-model="formData.decoration"
                placeholder="请选择"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                :disabled="true"
              />
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">项目进度</view>

            <tui-form-item
              prop="openTime"
              label="开盘时间"
              labelSize="30rpx"
              :labelColor="getLabelColor('openTime')"
              arrow
            >
              <picker
                mode="multiSelector"
                :range="openTimeRange"
                range-key="label"
                :value="openTimePickerValue"
                @change="onOpenTimeChange"
              >
                <tui-input
                  v-model="formData.openTime"
                  placeholder="请选择开盘时间"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>

            <tui-form-item
              prop="greenRate"
              label="绿化率"
              labelSize="30rpx"
              :labelColor="getLabelColor('greenRate')"
              @click="handleFormItemClick('greenRate')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.greenRate"
                  placeholder="请输入绿化率"
                  type="digit"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('greenRate')"
                  @blur="handleBlur('greenRate')"
                />
              </view>
              <template #right>
                <text>%</text>
              </template>
            </tui-form-item>

            <tui-form-item
              prop="plotRatio"
              label="容积率"
              labelSize="30rpx"
              :labelColor="getLabelColor('plotRatio')"
              @click="handleFormItemClick('plotRatio')"
            >
              <tui-input
                v-model="formData.plotRatio"
                placeholder="请输入容积率"
                type="digit"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('plotRatio')"
                @blur="handleBlur('plotRatio')"
              />
            </tui-form-item>

            <tui-form-item
              prop="loanPolicy"
              label="贷款政策"
              labelSize="30rpx"
              :labelColor="getLabelColor('loanPolicy')"
              :bottomBorder="false"
              @click="handleFormItemClick('loanPolicy')"
            >
              <tui-input
                v-model="formData.loanPolicy"
                placeholder="如：支持公积金贷款、商业贷款"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('loanPolicy')"
                @blur="handleBlur('loanPolicy')"
              />
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">小区情况</view>
            <tui-form-item
              prop="parkingInfo"
              label="车位租售"
              labelSize="30rpx"
              :labelColor="getLabelColor('parkingInfo')"
              @click="handleFormItemClick('parkingInfo')"
            >
              <tui-input
                v-model="formData.parkingInfo"
                placeholder="例如：租金300元/月，售价15万元/个"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('parkingInfo')"
                @blur="handleBlur('parkingInfo')"
              />
            </tui-form-item>
            <tui-form-item
              prop="plannedBuildings"
              label="规划楼栋"
              labelSize="30rpx"
              :labelColor="getLabelColor('plannedBuildings')"
              :bottomBorder="false"
              @click="handleFormItemClick('plannedBuildings')"
            >
              <tui-input
                v-model="formData.plannedBuildings"
                placeholder="例如：共12栋高层住宅"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('plannedBuildings')"
                @blur="handleBlur('plannedBuildings')"
              />
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">项目亮点</view>
            <text class="section-tip">选择项目特色标签，吸引更多客户关注</text>

            <view class="tags-container">
              <view
                v-for="tag in projectTags"
                :key="tag.value"
                class="tag-item"
                :class="{ active: formData.selectedTags.includes(tag.value) }"
                @tap="toggleTag(tag.value)"
              >
                {{ tag.label }}
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>项目图片</text>
            </view>
            <text class="section-tip"
              >至少上传1张图片，最多9张，展示项目真实面貌</text
            >

            <view class="image-upload-area">
              <view class="upload-grid">
                <view
                  v-for="(image, index) in formData.images"
                  :key="index"
                  class="image-item"
                >
                  <image :src="image" mode="aspectFill" class="preview-image" />
                  <view class="delete-btn" @tap="deleteImage(index)">
                    <text class="i-carbon-close"></text>
                  </view>
                </view>

                <view
                  v-if="formData.images.length < 9"
                  class="upload-btn"
                  @tap="uploadImage"
                >
                  <text class="i-carbon-camera upload-icon"></text>
                  <text class="upload-text">添加项目图片</text>
                  <text class="upload-text sub">展示楼盘亮点</text>
                </view>
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>项目描述</text>
            </view>
            <text class="section-tip"
              >详细介绍项目位置优势、周边配套等，提升项目吸引力</text
            >

            <tui-form-item
              prop="description"
              asteriskColor="#ff4757"
              :bottomBorder="false"
              padding="0 0"
              flexStart
            >
              <tui-textarea
                v-model="formData.description"
                placeholder="请描述项目的位置优势、周边配套、交通状况等信息"
                maxlength="500"
                height="200rpx"
                minHeight="200rpx"
                :borderTop="false"
                :borderBottom="false"
                backgroundColor="var(--bg-input)"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                showConfirmBar
                isCounter
                counterSize="24"
                counterColor="var(--text-info)"
                @input="handleInput('description')"
                @blur="handleBlur('description')"
              />
            </tui-form-item>
          </view>
        </tui-form>

        <view class="agreement-section">
          <view class="agreement-item" @tap="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreedToTerms }">
              <text v-if="agreedToTerms" class="i-carbon-checkmark"></text>
            </view>
            <text class="agreement-text">
              我承诺项目信息真实并同意《个人用户新房项目推广服务协议》
            </text>
          </view>
        </view>
      </view>

      <view class="submit-section">
        <tui-button
          @click="submitForm"
          type="primary"
          width="100%"
          height="96rpx"
          :bold="true"
          shape="circle"
        >
          免费发布
        </tui-button>
      </view>
    </view>

    <tui-bottom-popup :show="showOpenTimePicker" @close="closeOpenTimePicker">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-cancel" @tap="closeOpenTimePicker">取消</text>
          <text class="picker-title">开盘时间</text>
          <text class="picker-confirm" @tap="confirmOpenTime">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="openTimePickerValue"
          @change="onOpenTimeChange"
        >
          <picker-view-column>
            <view
              v-for="(time, index) in openTimeList"
              :key="index"
              class="picker-item"
            >
              {{ time }}
            </view>
          </picker-view-column>
        </picker-view>
      </view> </tui-bottom-popup
    >x
    <!-- 装修状况选择 -->
    <tui-bottom-popup
      :show="showDecorationPicker"
      @close="closeDecorationPicker"
    >
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-cancel" @tap="closeDecorationPicker">取消</text>
          <text class="picker-title">装修状况</text>
          <text class="picker-confirm" @tap="confirmDecoration">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="decorationPickerValue"
          @change="onDecorationPickerChange"
        >
          <picker-view-column>
            <view
              v-for="(decoration, index) in decorationList"
              :key="index"
              class="picker-item"
            >
              {{ decoration }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </tui-bottom-popup>

    <!-- 新增户型管理模态框 -->
    <tui-bottom-popup :show="showHouseTypeModal" @close="closeHouseTypeModal">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-cancel" @tap="closeHouseTypeModal">取消</text>
          <text class="picker-title">户型信息</text>
          <text class="picker-confirm" @tap="saveHouseType">确定</text>
        </view>
        <view class="house-type-form">
          <tui-form
            ref="houseTypeFormRef"
            :model="houseTypeForm"
            :rules="houseTypeRules"
          >
            <tui-form-item
              prop="name"
              label="户型名称"
              labelSize="30rpx"
              :labelColor="getLabelColor('name')"
              :asterisk="true"
              asteriskColor="#ff4757"
            >
              <tui-input
                v-model="houseTypeForm.name"
                placeholder="请输入户型名称,如:三室一厅"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
              />
            </tui-form-item>

            <tui-form-item
              prop="area"
              label="面积"
              labelSize="30rpx"
              :labelColor="getLabelColor('area')"
              :asterisk="true"
              asteriskColor="#ff4757"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="houseTypeForm.area"
                  placeholder="请输入面积"
                  type="digit"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                />
              </view>
              <template #right>
                <text>㎡</text>
              </template>
            </tui-form-item>

            <tui-form-item
              prop="images"
              label="户型图片"
              labelSize="30rpx"
              :bottomBorder="false"
            >
            </tui-form-item>
            <view class="image-upload-area">
              <view class="upload-grid">
                <view
                  v-for="(image, index) in houseTypeForm.images"
                  :key="index"
                  class="image-item"
                >
                  <image :src="image" mode="aspectFill" class="preview-image" />
                  <view class="delete-btn" @tap="deleteHouseTypeImage(index)">
                    <text class="i-carbon-close"></text>
                  </view>
                </view>

                <view
                  v-if="houseTypeForm.images.length < 9"
                  class="upload-btn"
                  @tap="uploadHouseTypeImage"
                >
                  <text class="i-carbon-camera upload-icon"></text>
                  <text class="upload-text">添加户型图片</text>
                  <text class="upload-text sub">展示户型特点</text>
                </view>
              </view>
            </view>
          </tui-form>
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import {
  PROJECT_TAGS_OPTIONS,
  PROPERTY_YEARS_OPTIONS,
  SALES_STATUS_OPTIONS,
  BUILDING_TYPE_OPTIONS,
  DECORATION_OPTIONS,
  BUILD_YEAR_OPTIONS,
  OPENING_TIME_OPTIONS,
} from "@/constants";

const formRef = ref<any>(null);

const formData = reactive({
  projectName: "",
  address: "",
  developer: "",
  propertyCompany: "",
  propertyFee: "",
  buildYear: "",
  propertyYears: "",
  status: "",
  averagePrice: "",
  priceRangeMin: "",
  priceRangeMax: "",
  houseTypes: "",
  buildingType: "",
  decoration: "",
  openTime: "",
  selectedTags: [] as string[],
  images: [] as string[],
  description: "",
  greenRate: "",
  plotRatio: "",
  loanPolicy: "",
  parkingInfo: "",
  plannedBuildings: "",
  houseTypeDetails: [] as Array<{
    id: string;
    name: string;
    area: string;
    images: string[];
  }>,
});

const agreedToTerms = ref(false);

const projectTags = PROJECT_TAGS_OPTIONS;

const showHouseTypeModal = ref(false);
const currentEditingHouseType = ref<any>(null);
const houseTypeForm = reactive({
  name: "",
  area: "",
  images: [] as string[],
});
const houseTypeFormRef = ref<any>(null);

const showOpenTimePicker = ref(false);
const showBuildingTypePicker = ref(false);
const showDecorationPicker = ref(false);

// 选择器数据列表
const openTimeRange = (() => {
  const years = [];
  const currentYear = new Date().getFullYear();
  for (let i = currentYear - 5; i <= currentYear + 10; i++) {
    years.push({ label: `${i}年`, value: i });
  }
  const months = [];
  for (let i = 1; i <= 12; i++) {
    months.push({ label: `${i}月`, value: i });
  }
  return [years, months];
})();
const decorationList = DECORATION_OPTIONS.map((item) => item.label);

const fieldFocusState = reactive({
  projectName: false,
  address: false,
  developer: false,
  propertyCompany: false,
  propertyFee: false,
  buildYear: false,
  averagePrice: false,
  priceRangeMin: false,
  priceRangeMax: false,
  houseTypes: false,
  openTime: false,
  description: false,
  greenRate: false,
  plotRatio: false,
  loanPolicy: false,
  parkingInfo: false,
  plannedBuildings: false,
});

const formRules = {
  projectName: {
    name: "projectName",
    rule: ["required"],
    msg: ["请输入楼盘名称"],
  },
  address: {
    name: "address",
    rule: ["required"],
    msg: ["请输入项目地址"],
  },
  developer: {
    name: "developer",
    rule: ["required"],
    msg: ["请输入开发商名称"],
  },
  averagePrice: {
    name: "averagePrice",
    rule: ["required", "number"],
    msg: ["请输入均价", "请输入正确的价格"],
  },
  description: {
    name: "description",
    rule: ["required"],
    msg: ["请输入项目描述"],
  },
};

const houseTypeRules = {
  name: {
    name: "name",
    rule: ["required"],
    msg: ["请输入户型名称"],
  },
  area: {
    name: "area",
    rule: ["required", "number"],
    msg: ["请输入面积", "请输入正确的面积"],
  },
};

const propertyYearsPickerValue = ref(0);
const statusPickerValue = ref(0);
const buildingTypePickerValue = ref(0);
const decorationPickerValue = ref(0);
const buildYearPickerValue = ref(0);
const openTimePickerValue = ref([5, new Date().getMonth()]);

const getLabelColor = (field: string) => {
  const value = formData[field as keyof typeof formData];
  if (typeof value === "string") {
    return value ? "var(--text-info)" : "var(--text-secondary)";
  }
  return "var(--text-secondary)";
};

let inputTimer: any = null;
const handleInput = (field: string) => {
  if (inputTimer) clearTimeout(inputTimer);
  inputTimer = setTimeout(() => {
    if (formRef.value) {
      requestAnimationFrame(() => {
        formRef.value.validateField(field);
      });
    }
  }, 300);
};

const handleBlur = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = false;
};

const handleFormItemClick = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = true;
};

const toggleTag = (tag: string) => {
  const index = formData.selectedTags.indexOf(tag);
  if (index > -1) {
    formData.selectedTags.splice(index, 1);
  } else {
    formData.selectedTags.push(tag);
  }
};

const onPropertyYearsChange = (e: any) => {
  const index = e.detail.value;
  propertyYearsPickerValue.value = index;
  formData.propertyYears = PROPERTY_YEARS_OPTIONS[index].label;
};

const onStatusChange = (e: any) => {
  const index = e.detail.value;
  statusPickerValue.value = index;
  formData.status = SALES_STATUS_OPTIONS[index].label;
};

const onBuildingTypeChange = (e: any) => {
  const index = e.detail.value;
  buildingTypePickerValue.value = index;
  formData.buildingType = BUILDING_TYPE_OPTIONS[index].label;
};

const onDecorationPickerChange = (e: any) => {
  decorationPickerValue.value = e.detail.value[0];
};

const onBuildYearChange = (e: any) => {
  const index = e.detail.value;
  buildYearPickerValue.value = index;
  formData.buildYear = BUILD_YEAR_OPTIONS[index].label;
};

const onOpenTimeChange = (e: any) => {
  const [yearIndex, monthIndex] = e.detail.value;
  openTimePickerValue.value = e.detail.value;
  const year = openTimeRange[0][yearIndex].label;
  const month = openTimeRange[1][monthIndex].label;
  formData.openTime = `${year}${month}`;
};

const uploadImage = () => {
  uni.chooseImage({
    count: 9 - formData.images.length,
    sizeType: ["compressed"],
    sourceType: ["camera", "album"],
    success: (res) => {
      if (Array.isArray(res.tempFilePaths)) {
        res.tempFilePaths.forEach((path) => formData.images.push(path));
      }
    },
  });
};

const deleteImage = (index: number) => {
  formData.images.splice(index, 1);
};

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const submitForm = async () => {
  try {
    if (formData.images.length === 0) {
      uni.showToast({
        title: "请至少上传1张项目图片",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    if (!agreedToTerms.value) {
      uni.showToast({
        title: "请先同意服务协议",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    if (formRef.value) {
      const validateResult = await formRef.value.validate();
      if (!validateResult.isPass) {
        uni.showToast({
          title: validateResult.errorMsg || "请完善必填信息",
          icon: "none",
          duration: 2000,
        });
        return;
      }
    }

    await publishProject();
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

const publishProject = async () => {
  try {
    uni.showLoading({ title: "发布中..." });

    const publishData = {
      ...formData,
    };

    console.log("发布数据:", publishData);

    await new Promise((resolve) => setTimeout(resolve, 2000));
    uni.hideLoading();

    uni.showToast({
      title: "发布成功",
      icon: "success",
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      },
    });
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

const goBack = () => {
  uni.navigateBack();
};

const openHouseTypeModal = (houseType?: any) => {
  if (houseType) {
    currentEditingHouseType.value = houseType;
    houseTypeForm.name = houseType.name;
    houseTypeForm.area = houseType.area;
    houseTypeForm.images = [...houseType.images];
  } else {
    currentEditingHouseType.value = null;
    houseTypeForm.name = "";
    houseTypeForm.area = "";
    houseTypeForm.images = [];
  }
  showHouseTypeModal.value = true;
};

const closeHouseTypeModal = () => {
  showHouseTypeModal.value = false;
  currentEditingHouseType.value = null;
};

const saveHouseType = async () => {
  if (houseTypeFormRef.value) {
    const validateResult = await houseTypeFormRef.value.validate();
    if (!validateResult.isPass) {
      uni.showToast({
        title: validateResult.errorMsg || "请完善户型信息",
        icon: "none",
        duration: 2000,
      });
      return;
    }
  }

  if (houseTypeForm.images.length === 0) {
    uni.showToast({
      title: "请上传至少1张户型图",
      icon: "none",
    });
    return;
  }

  if (currentEditingHouseType.value) {
    const index = formData.houseTypeDetails.findIndex(
      (item) => item.id === currentEditingHouseType.value.id
    );
    if (index > -1) {
      formData.houseTypeDetails[index] = {
        ...currentEditingHouseType.value,
        name: houseTypeForm.name,
        area: houseTypeForm.area,
        images: [...houseTypeForm.images],
      };
    }
  } else {
    formData.houseTypeDetails.push({
      id: Date.now().toString(),
      name: houseTypeForm.name,
      area: houseTypeForm.area,
      images: [...houseTypeForm.images],
    });
  }

  closeHouseTypeModal();

  updateMainHouseTypes();
};

const deleteHouseType = (index: number) => {
  uni.showModal({
    title: "确认删除",
    content: "确定要删除这个户型吗？",
    success: (res) => {
      if (res.confirm) {
        formData.houseTypeDetails.splice(index, 1);
        updateMainHouseTypes();
      }
    },
  });
};

const updateMainHouseTypes = () => {
  if (formData.houseTypeDetails.length === 0) {
    formData.houseTypes = "";
  } else {
    const types = formData.houseTypeDetails.map(
      (item) => `${item.area}㎡ ${item.name}`
    );
    formData.houseTypes = types.join("、");
  }
};

const uploadHouseTypeImage = () => {
  uni.chooseImage({
    count: 9 - houseTypeForm.images.length,
    sizeType: ["compressed"],
    sourceType: ["camera", "album"],
    success: (res) => {
      if (Array.isArray(res.tempFilePaths)) {
        res.tempFilePaths.forEach((path) => houseTypeForm.images.push(path));
      }
    },
  });
};

const deleteHouseTypeImage = (index: number) => {
  houseTypeForm.images.splice(index, 1);
};

const selectBuildingType = () => {
  showBuildingTypePicker.value = true;
};

const selectDecoration = () => {
  showDecorationPicker.value = true;
};

const closeOpenTimePicker = () => {
  showOpenTimePicker.value = false;
};

const closeBuildingTypePicker = () => {
  showBuildingTypePicker.value = false;
};

const closeDecorationPicker = () => {
  showDecorationPicker.value = false;
};

const confirmOpenTime = () => {
  const [yearIndex, monthIndex] = openTimePickerValue.value;
  const year = openTimeRange[0][yearIndex].label;
  const month = openTimeRange[1][monthIndex].label;
  formData.openTime = `${year}${month}`;
  closeOpenTimePicker();
};

const confirmDecoration = () => {
  formData.decoration = DECORATION_OPTIONS[decorationPickerValue.value].label;
  closeDecorationPicker();
};

const openTimeList = OPENING_TIME_OPTIONS.map((item) => item.label);
</script>

<style lang="scss" scoped>
.new-house-publish-page {
  min-height: 100vh;
  background: var(--bg-page);
}

.content {
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
  background: transparent;
  box-sizing: border-box;
  will-change: scroll-position;
}

.form-content {
  padding-bottom: var(--spacing-32);
}

.card {
  margin-bottom: 24rpx;
}

.guide-section {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border-radius: var(--radius-lg);
  padding: var(--spacing-20);
  margin-bottom: var(--spacing-16);
  text-align: center;
  box-shadow: 0 8rpx 20rpx rgba(139, 92, 246, 0.15);
}

.guide-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-inverse);
  display: block;
  margin-bottom: var(--spacing-6);
}

.guide-desc {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.9);
  display: block;
  line-height: var(--line-height-normal);
}

.form-section {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-20);
  margin-bottom: var(--spacing-12);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin: 12rpx 0 12rpx 12rpx;
  display: flex;
  align-items: center;
}

.section-tip {
  font-size: var(--font-size-xs);
  color: var(--text-info);
  margin-bottom: var(--spacing-16);
  display: block;
  line-height: var(--line-height-normal);
}

.required {
  color: var(--text-red);
  margin-right: var(--spacing-4);
  font-weight: var(--font-weight-bold);
}

.form-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-16) 0;
  border-bottom: 1rpx solid var(--border-color);
  min-height: 88rpx;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: var(--font-size-base);
  color: var(--text-base);
  min-width: 160rpx;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
}

.value-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.input {
  flex: 1;
  text-align: right;
  font-size: var(--font-size-base);
  color: var(--text-base);
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: var(--text-grey);
  }
}

.unit {
  font-size: var(--font-size-base);
  color: var(--text-info);
  margin-left: var(--spacing-4);
  font-weight: var(--font-weight-medium);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-8);
}

.tag-item {
  padding: var(--spacing-6) var(--spacing-12);
  border-radius: var(--radius);
  background: var(--bg-tag);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &.active {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    border-color: #8b5cf6;
  }

  &:active {
    transform: scale(0.95);
  }
}

.image-upload-area {
  margin-top: var(--spacing-8);
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-8);
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius);
  overflow: hidden;
  background: var(--bg-tag);
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: 20rpx;

  &:active {
    transform: scale(0.9);
  }
}

.upload-btn {
  aspect-ratio: 1;
  border: 2rpx dashed var(--text-grey);
  border-radius: var(--radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-info);
  background: var(--bg-tag);
  transition: all 0.3s ease;

  &:active {
    border-color: #8b5cf6;
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
  }
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: var(--spacing-4);
}

.upload-text {
  font-size: var(--font-size-xs);
  text-align: center;
  line-height: var(--line-height-normal);

  &.sub {
    margin-top: 2rpx;
    opacity: 0.8;
  }
}

.textarea-container {
  position: relative;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--spacing-12);
  background: var(--bg-input);
  margin-top: var(--spacing-8);
}

.description-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: var(--font-size-base);
  line-height: var(--line-height-loose);
  color: var(--text-base);
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: var(--text-grey);
  }
}

.char-count {
  position: absolute;
  bottom: var(--spacing-8);
  right: var(--spacing-12);
  font-size: var(--font-size-xs);
  color: var(--text-info);
}

.agreement-section {
  margin-bottom: var(--spacing-20);
  padding: var(--spacing-16);
  background: var(--bg-card);
  border-radius: var(--radius);
  border: 1rpx solid var(--border-color);
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-8) 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--text-grey);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-12);
  margin-top: 2rpx;
  transition: all 0.2s ease;

  &.checked {
    background: #8b5cf6;
    border-color: #8b5cf6;
    color: var(--text-inverse);
  }

  &:active {
    transform: scale(0.9);
  }
}

.agreement-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: var(--line-height-loose);
  flex: 1;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-16) var(--spacing-16);
  padding-bottom: calc(var(--spacing-20) + env(safe-area-inset-bottom));
  background: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
  will-change: transform;
  transform: translateZ(0);
}

.service-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-12) var(--spacing-16);
  margin-bottom: var(--spacing-12);
  background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
  border-radius: var(--radius);
  border: 1rpx solid #d1e9ff;
}

.summary-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--primary);
}

.summary-benefit {
  font-size: var(--font-size-xs);
  color: var(--text-info);
  background: var(--primary);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.input-with-unit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

.picker-container {
  padding: 32rpx;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-16) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.picker-cancel,
.picker-confirm {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.picker-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  display: block;
  margin-bottom: var(--spacing-4);
}

.picker-view {
  height: 400rpx;
  overflow: hidden;
}

.picker-view-column {
  height: 100%;
}

.picker-item {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  color: var(--text-base);
}

.house-type-form {
  padding: var(--spacing-16);
  height: 70vh;
}

// 户型列表样式
.house-type-list {
  margin-top: var(--spacing-16);
  background: var(--bg-tag);
  border-radius: var(--radius);
  padding: var(--spacing-12);
}

.house-type-list-title {
  font-size: var(--font-size-sm);
  color: var(--text-info);
  margin-bottom: var(--spacing-12);
  font-weight: var(--font-weight-medium);
}

.house-type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-12) 0;
  border-bottom: 1rpx solid var(--border-color);

  &:last-child {
    border-bottom: none;
  }
}

.house-type-info {
  flex: 1;
}

.house-type-name {
  font-size: var(--font-size-base);
  color: var(--text-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: 4rpx;
}

.house-type-area {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 4rpx;
}

.house-type-images {
  font-size: var(--font-size-xs);
  color: var(--text-info);
}

.house-type-actions {
  display: flex;
  gap: var(--spacing-12);
}

.action-btn {
  font-size: var(--font-size-xs);
  padding: 6rpx 12rpx;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;

  &.edit {
    background: rgba(79, 172, 254, 0.1);
    color: var(--primary);
    border: 1rpx solid rgba(79, 172, 254, 0.3);
  }

  &.delete {
    background: rgba(255, 71, 87, 0.1);
    color: var(--text-red);
    border: 1rpx solid rgba(255, 71, 87, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
}

.price-range-inputs {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  gap: 8rpx;
}

.price-range-separator {
  padding: 0 8rpx;
  color: var(--text-secondary);
}
</style>
