<template>
  <view class="rent-manage-page">
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
      title="租房管理"
      left-icon="back"
      @clickLeft="goBack"
    />

    <view class="content">
      <view class="stats-bar">
        <view class="stat-item">
          <text class="stat-number">{{ totalRentals }}</text>
          <text class="stat-label">总房源</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ publishedRentals }}</text>
          <text class="stat-label">已发布</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ viewsCount }}</text>
          <text class="stat-label">总浏览</text>
        </view>
      </view>

      <view class="filter-bar">
        <view class="filter-tabs">
          <view 
            v-for="status in statusFilters"
            :key="status.value"
            class="filter-tab"
            :class="{ active: activeFilter === status.value }"
            @tap="switchFilter(status.value)"
          >
            {{ status.label }}
          </view>
        </view>
        <view class="action-btn" @tap="addRental">
          <text class="i-carbon-add text-24rpx"></text>
          <text class="action-text">发布</text>
        </view>
      </view>

      <scroll-view scroll-y class="list-container">
        <view v-if="filteredRentals.length === 0" class="empty-state">
          <image src="/static/images/empty-house.png" class="empty-image" mode="aspectFit" />
          <text class="empty-title">暂无租房信息</text>
          <text class="empty-desc">快去发布您的第一个房源吧</text>
          <tui-button 
            type="primary"
            width="200rpx"
            height="64rpx"
            shape="circle"
            @click="addRental"
          >
            立即发布
          </tui-button>
        </view>

        <view v-else>
          <view 
            v-for="item in filteredRentals"
            :key="item.id"
            class="rental-item"
            @tap="viewDetail(item)"
          >
            <view class="item-header">
              <view class="title-row">
                <text class="house-title">{{ item.title }}</text>
                <view class="status-tag" :class="item.status">
                  {{ getStatusText(item.status) }}
                </view>
              </view>
              <view class="location-row">
                <text class="location">{{ item.location }}</text>
                <text class="price">¥{{ item.price }}/月</text>
              </view>
            </view>

            <view class="item-info">
              <view class="info-item">
                <text class="info-label">房型：</text>
                <text class="info-value">{{ item.houseType }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">面积：</text>
                <text class="info-value">{{ item.area }}㎡</text>
              </view>
              <view class="info-item">
                <text class="info-label">浏览：</text>
                <text class="info-value">{{ item.views }}次</text>
              </view>
              <view class="info-item">
                <text class="info-label">发布：</text>
                <text class="info-value">{{ item.publishTime }}</text>
              </view>
            </view>

            <view class="item-actions">
              <view class="action-button" @tap.stop="editRental(item)">
                <text class="i-carbon-edit text-24rpx"></text>
                <text>编辑</text>
              </view>
              <view class="action-button" @tap.stop="toggleStatus(item)">
                <text class="i-carbon-view text-24rpx"></text>
                <text>{{ item.status === 'published' ? '下架' : '上架' }}</text>
              </view>
              <view class="action-button danger" @tap.stop="deleteRental(item)">
                <text class="i-carbon-trash-can text-24rpx"></text>
                <text>删除</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface RentalItem {
  id: string;
  title: string;
  location: string;
  price: number;
  houseType: string;
  area: number;
  views: number;
  publishTime: string;
  status: 'published' | 'draft' | 'offline';
}

// 模拟数据
const rentals = ref<RentalItem[]>([
  {
    id: '1',
    title: '精装修两居室，南北通透，拎包入住',
    location: '朝阳区 · 望京',
    price: 6500,
    houseType: '2室1厅',
    area: 85,
    views: 128,
    publishTime: '2024-01-15',
    status: 'published'
  },
  {
    id: '2',
    title: '地铁沿线，交通便利，设施齐全',
    location: '海淀区 · 中关村',
    price: 8800,
    houseType: '1室1厅',
    area: 65,
    views: 89,
    publishTime: '2024-01-10',
    status: 'published'
  },
  {
    id: '3',
    title: '商圈核心，生活配套完善',
    location: '朝阳区 · 国贸',
    price: 12000,
    houseType: '3室2厅',
    area: 120,
    views: 256,
    publishTime: '2024-01-08',
    status: 'offline'
  }
]);

const statusFilters = [
  { label: '全部', value: 'all' },
  { label: '已发布', value: 'published' },
  { label: '已下架', value: 'offline' },
  { label: '草稿', value: 'draft' }
];

const activeFilter = ref('all');

// 统计数据
const totalRentals = computed(() => rentals.value.length);
const publishedRentals = computed(() => rentals.value.filter(item => item.status === 'published').length);
const viewsCount = computed(() => rentals.value.reduce((sum, item) => sum + item.views, 0));

// 过滤后的数据
const filteredRentals = computed(() => {
  if (activeFilter.value === 'all') {
    return rentals.value;
  }
  return rentals.value.filter(item => item.status === activeFilter.value);
});

// 切换过滤器
const switchFilter = (filter: string) => {
  activeFilter.value = filter;
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    published: '已发布',
    draft: '草稿',
    offline: '已下架'
  };
  return statusMap[status as keyof typeof statusMap] || '未知';
};

// 添加房源
const addRental = () => {
  uni.navigateTo({
    url: '/pages/house/publish/rent'
  });
};

// 查看详情
const viewDetail = (item: RentalItem) => {
  uni.navigateTo({
    url: `/pages/house/detail/index?id=${item.id}`
  });
};

// 编辑房源
const editRental = (item: RentalItem) => {
  uni.navigateTo({
    url: `/pages/house/publish/rent?id=${item.id}&mode=edit`
  });
};

// 切换状态
const toggleStatus = (item: RentalItem) => {
  const newStatus = item.status === 'published' ? 'offline' : 'published';
  const actionText = newStatus === 'published' ? '上架' : '下架';
  
  uni.showModal({
    title: '确认操作',
    content: `确定要${actionText}这个房源吗？`,
    success: (res) => {
      if (res.confirm) {
        item.status = newStatus;
        uni.showToast({
          title: `${actionText}成功`,
          icon: 'success'
        });
      }
    }
  });
};

// 删除房源
const deleteRental = (item: RentalItem) => {
  uni.showModal({
    title: '确认删除',
    content: '删除后不可恢复，确定要删除这个房源吗？',
    success: (res) => {
      if (res.confirm) {
        const index = rentals.value.findIndex(rental => rental.id === item.id);
        if (index > -1) {
          rentals.value.splice(index, 1);
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.rent-manage-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.content {
  padding-top: 88rpx;
}

.stats-bar {
  display: flex;
  background: white;
  padding: 32rpx;
  margin: 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  text-align: center;
  
  .stat-number {
    font-size: 36rpx;
    font-weight: 700;
    color: #ff6d00;
    display: block;
    margin-bottom: 8rpx;
  }
  
  .stat-label {
    font-size: 24rpx;
    color: #666;
    display: block;
  }
}

.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: white;
  margin: 0 16rpx 16rpx;
  border-radius: 16rpx;
}

.filter-tabs {
  display: flex;
  gap: 24rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  background: #f5f5f5;
  transition: all 0.2s;
  
  &.active {
    background: #ff6d00;
    color: white;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #ff6d00;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.list-container {
  height: calc(100vh - 300rpx);
  padding: 0 16rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin: 0 auto 32rpx;
    opacity: 0.6;
  }
  
  .empty-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
    display: block;
    margin-bottom: 12rpx;
  }
  
  .empty-desc {
    font-size: 26rpx;
    color: #999;
    display: block;
    margin-bottom: 32rpx;
  }
}

.rental-item {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  
  &:active {
    transform: scale(0.98);
  }
}

.item-header {
  margin-bottom: 16rpx;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.house-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  
  &.published {
    background: #e8f5e8;
    color: #52c41a;
  }
  
  &.offline {
    background: #fff2e8;
    color: #fa8c16;
  }
  
  &.draft {
    background: #f0f0f0;
    color: #666;
  }
}

.location-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location {
  font-size: 26rpx;
  color: #666;
}

.price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6d00;
}

.item-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.info-label {
  color: #999;
  margin-right: 8rpx;
}

.info-value {
  color: #666;
}

.item-actions {
  display: flex;
  gap: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 16rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s;
  
  &:active {
    transform: scale(0.95);
  }
  
  &.danger {
    background: #ffebee;
    color: #f44336;
  }
}
</style>
