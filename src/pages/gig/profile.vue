<template>
  <view class="user-profile-page bg-page min-h-screen">
    <scroll-view scroll-y class="flex-1">
      <!-- 用户基础信息 -->
      <view class="profile-header card bg-white m-30rpx p-30rpx rounded-lg shadow-sm flex items-center">
        <image :src="user.avatar" class="avatar w-120rpx h-120rpx rounded-full mr-30rpx" />
        <view class="flex-1">
          <view class="flex items-center">
            <text class="name font-bold text-lg">{{ user.name }}</text>
            <text v-if="user.verified" class="i-carbon-badge-filled text-blue-500 ml-10rpx"></text>
          </view>
          <text class="bio text-info text-sm mt-10rpx">{{ user.bio }}</text>
        </view>
      </view>

      <!-- 数据统计 -->
      <view class="stats-card card bg-white m-30rpx p-30rpx rounded-lg shadow-sm flex justify-around">
        <view class="stat-item text-center">
          <text class="value font-bold text-xl">{{ user.stats.completed_gigs }}</text>
          <text class="label block text-info text-sm">完成任务</text>
        </view>
        <view class="stat-item text-center">
          <text class="value font-bold text-xl">{{ user.stats.rating.toFixed(1) }}</text>
          <text class="label block text-info text-sm">平均评分</text>
        </view>
        <view class="stat-item text-center">
          <text class="value font-bold text-xl">{{ user.stats.on_time_rate }}%</text>
          <text class="label block text-info text-sm">准时率</text>
        </view>
      </view>

      <!-- 技能标签 -->
      <view class="skills-card card bg-white m-30rpx p-30rpx rounded-lg shadow-sm">
        <text class="card-title font-bold mb-20rpx block">技能</text>
        <view class="tags flex flex-wrap">
          <view v-for="(skill, index) in user.skills" :key="index" class="tag bg-tag text-secondary text-sm px-16rpx py-8rpx rounded mr-16rpx mb-16rpx">
            {{ skill }}
          </view>
        </view>
      </view>

      <!-- 评价与历史 -->
      <view class="reviews-card card bg-white m-30rpx p-30rpx rounded-lg shadow-sm">
        <text class="card-title font-bold mb-20rpx block">评价与工作历史</text>
        <view class="review-list">
          <view v-for="review in reviews" :key="review.id" class="review-item py-20rpx border-b border-color last:border-b-0">
            <view class="flex justify-between items-center mb-10rpx">
              <text class="gig-title font-semibold">{{ review.gig_title }}</text>
              <view class="rating flex items-center">
                <text class="i-carbon-star-filled text-yellow-400 mr-4rpx" v-for="i in review.rating" :key="i"></text>
              </view>
            </view>
            <text class="comment text-secondary mb-10rpx block">{{ review.comment }}</text>
            <view class="reviewer flex items-center">
              <image :src="review.reviewer.avatar" class="w-40rpx h-40rpx rounded-full mr-10rpx" />
              <text class="text-info text-sm">{{ review.reviewer.name }} | {{ review.date }}</text>
            </view>
          </view>
           <view v-if="!reviews.length" class="empty-state text-center py-40rpx">
            <text class="text-info">暂无评价</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const user = ref({
  name: '小明',
  avatar: 'https://randomuser.me/api/portraits/men/42.jpg',
  verified: true,
  bio: '靠谱的零工达人，随叫随到！',
  stats: {
    completed_gigs: 25,
    rating: 4.9,
    on_time_rate: 100,
  },
  skills: ['传单派发', '活动执行', '打包分拣', '问卷调查', '产品推广'],
});

const reviews = ref([
  {
    id: 1,
    gig_title: '周末商场促销活动协助',
    rating: 5,
    comment: '非常给力的小伙伴，下次还找你！'
    ,
    reviewer: {
      name: '李老板',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    },
    date: '2025-06-28',
  },
  {
    id: 2,
    gig_title: '新品奶茶试饮派发',
    rating: 5,
    comment: '认真负责，效率很高。'
    ,
    reviewer: {
      name: '奶茶店店长',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    },
    date: '2025-06-20',
  },
]);

</script>

<style lang="scss" scoped>
.user-profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.profile-header {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.bio {
  font-size: 26rpx;
  color: #999;
  margin-top: 8rpx;
}

.verified-icon {
  color: #007bff;
  margin-left: 12rpx;
  font-size: 32rpx;
}

.stats-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;

  .value {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
  }

  .label {
    font-size: 24rpx;
    color: #999;
  }
}

.skills-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  background-color: #e0f7fa;
  color: #00796b;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
}

.reviews-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.review-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.gig-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.rating {
  display: flex;
  align-items: center;
}

.text-yellow-400 {
  color: #ffc107;
}

.comment {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.reviewer {
  display: flex;
  align-items: center;
}

.reviewer-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  border: 1rpx solid #eee;
}

.reviewer-info {
  font-size: 24rpx;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 40rpx;

  .text-info {
    font-size: 28rpx;
    color: #999;
  }
}
</style>