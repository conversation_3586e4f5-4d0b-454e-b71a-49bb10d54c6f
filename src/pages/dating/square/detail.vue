<template>
  <view class="post-detail-container">
    <uni-nav-bar title="动态详情" :fixed="true" statusBar :border="false" left-icon="back" @clickLeft="handleBack" />
    
    <!-- 帖子内容区域 -->
    <scroll-view scroll-y class="content-scroll" :style="{ height: contentHeight }">
      <!-- 帖子主体 -->
      <view class="post-main" v-if="postDetail">
        <view class="post-header">
          <view class="user-info">
            <image class="avatar" :src="postDetail.user.avatar" mode="aspectFill" @click="goToUserProfile" />
            <view class="user-details">
              <text class="user-name">{{ postDetail.user.name }}</text>
              <view class="meta-info">
                <text>{{ postDetail.timeAgo }}</text>
                <text class="dot">·</text>
                <text>{{ postDetail.distance }}</text>
              </view>
            </view>
          </view>
          <view class="actions">
            <button class="follow-btn" @click="toggleFollow">{{ postDetail.user.isFollowed ? '已关注' : '关注' }}</button>
            <uni-icons type="more-filled" size="24" color="#9CA3AF" @click="showMoreActions"></uni-icons>
          </view>
        </view>

        <view class="post-body">
          <text class="content-text">{{ postDetail.content }}</text>
          <view v-if="postDetail.images && postDetail.images.length" class="image-grid" :class="`grid-${postDetail.images.length}`">
            <image 
              v-for="(image, index) in postDetail.images" 
              :key="index" 
              :src="image" 
              class="grid-image" 
              mode="aspectFill" 
              @click="previewImage(index)" 
            />
          </view>
        </view>

        <view class="post-footer">
          <view class="topic-tags">
            <view v-for="(topic, index) in postDetail.topics" :key="index" class="topic-tag">
              {{ topic }}
            </view>
          </view>
          <view class="social-stats">
            <view class="stat-item" @click="toggleLike">
              <uni-icons :type="postDetail.isLiked ? 'heart-filled' : 'heart'" :color="postDetail.isLiked ? '#EF4444' : '#6B7280'" size="22"></uni-icons>
              <text class="stat-count">{{ postDetail.likes }}</text>
            </view>
            <view class="stat-item">
              <uni-icons type="chatbubble" color="#6B7280" size="22"></uni-icons>
              <text class="stat-count">{{ comments.length }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 评论区域 -->
      <view class="comments-section">
        <view class="comments-header">
          <text class="comments-title">评论 ({{ comments.length }})</text>
        </view>
        <CommentList :comments="comments" @on-action="handleCommentAction" />
      </view>
    </scroll-view>

    <!-- 底部评论输入 -->
    <CommentInput @on-submit="handleCommentSubmit" @on-emoji="showEmojiPicker" />

    <!-- 举报弹窗 -->
    <ReportModal 
      :visible="showReportModal" 
      :type="reportType"
      :target-id="reportTargetId"
      @close="showReportModal = false"
      @submit="handleReport"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import CustomNavBar from '@/components/CustomNavBar.vue';
import CommentList from '@/components/dating/square/CommentList.vue';
import CommentInput from '@/components/dating/square/CommentInput.vue';
import ReportModal from '@/components/dating/square/ReportModal.vue';

interface User {
  id: number;
  name: string;
  avatar: string;
  isFollowed?: boolean;
}

interface Post {
  id: number;
  user: User;
  content: string;
  images?: string[];
  topics: string[];
  likes: number;
  comments: number;
  isLiked: boolean;
  distance: string;
  timeAgo: string;
}

interface Comment {
  id: number;
  user: User;
  content: string;
  timeAgo: string;
  likes: number;
  isLiked: boolean;
  replies?: Comment[];
  replyTo?: string;
}

const postId = ref<string | null>(null);
const postDetail = ref<Post | null>(null);
const comments = ref<Comment[]>([]);
const showReportModal = ref(false);
const reportType = ref<'post' | 'comment'>('post');
const reportTargetId = ref<number>(0);

const contentHeight = computed(() => {
  const systemInfo = uni.getSystemInfoSync();
  const statusBarHeight = systemInfo.statusBarHeight || 44;
  const navBarHeight = 88;
  const inputHeight = 120;
  return `${systemInfo.windowHeight - statusBarHeight - navBarHeight - inputHeight}px`;
});

onLoad((options) => {
  if (options && options.id) {
    postId.value = options.id;
    loadPostDetail();
    loadComments();
  }
});

const loadPostDetail = () => {
  // 模拟加载帖子详情
  setTimeout(() => {
    postDetail.value = {
      id: parseInt(postId.value || '1'),
      user: {
        id: 1,
        name: '思思',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
        isFollowed: false
      },
      content: '今天天气真好，出来爬山，有人一起吗？欢迎来偶遇哦～ 这里的风景真的太美了，心情瞬间变好！',
      images: [
        'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=60',
        'https://images.unsplash.com/photo-1554034483-04fda0d3507b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=60',
        'https://images.unsplash.com/photo-1562620459-5f75c53b1d06?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=60'
      ],
      topics: ['#周末去哪儿', '#户外运动', '#爬山'],
      likes: 128,
      comments: 15,
      isLiked: false,
      distance: '2.3km',
      timeAgo: '2小时前'
    };
  }, 500);
};

const loadComments = () => {
  // 模拟加载评论
  setTimeout(() => {
    comments.value = [
      {
        id: 1,
        user: {
          id: 2,
          name: '小甜豆',
          avatar: 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        },
        content: '哇，风景真的好美！我也想去爬山了 🏔️',
        timeAgo: '1小时前',
        likes: 12,
        isLiked: false,
        replies: [
          {
            id: 11,
            user: {
              id: 1,
              name: '思思',
              avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            content: '一起来呀！明天还要去呢',
            timeAgo: '50分钟前',
            likes: 3,
            isLiked: true,
            replyTo: '小甜豆'
          }
        ]
      },
      {
        id: 2,
        user: {
          id: 3,
          name: '一只小耳朵',
          avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        },
        content: '这是哪里呀？看起来好棒的样子！',
        timeAgo: '30分钟前',
        likes: 5,
        isLiked: false
      }
    ];
  }, 300);
};

const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: postDetail.value?.images || []
  });
};

const toggleLike = () => {
  if (postDetail.value) {
    postDetail.value.isLiked = !postDetail.value.isLiked;
    postDetail.value.likes += postDetail.value.isLiked ? 1 : -1;
  }
};

const toggleFollow = () => {
  if (postDetail.value?.user) {
    postDetail.value.user.isFollowed = !postDetail.value.user.isFollowed;
    uni.showToast({
      title: postDetail.value.user.isFollowed ? '关注成功' : '取消关注',
      icon: 'none'
    });
  }
};

const goToUserProfile = () => {
  uni.navigateTo({
    url: `/pages/dating/user-detail?userId=${postDetail.value?.user.id}`
  });
};

const showMoreActions = () => {
  uni.showActionSheet({
    itemList: ['举报', '分享'],
    success: (res) => {
      if (res.tapIndex === 0) {
        reportType.value = 'post';
        reportTargetId.value = postDetail.value?.id || 0;
        showReportModal.value = true;
      } else if (res.tapIndex === 1) {
        uni.showToast({ title: '分享功能开发中', icon: 'none' });
      }
    }
  });
};

const handleCommentAction = (action: { type: string; payload: any }) => {
  switch (action.type) {
    case 'like':
      toggleCommentLike(action.payload.id);
      break;
    case 'reply':
      // 处理回复
      console.log('Reply to comment:', action.payload);
      break;
    case 'report':
      reportType.value = 'comment';
      reportTargetId.value = action.payload.id;
      showReportModal.value = true;
      break;
  }
};

const toggleCommentLike = (commentId: number) => {
  const findAndToggle = (commentList: Comment[]) => {
    for (const comment of commentList) {
      if (comment.id === commentId) {
        comment.isLiked = !comment.isLiked;
        comment.likes += comment.isLiked ? 1 : -1;
        return true;
      }
      if (comment.replies && findAndToggle(comment.replies)) {
        return true;
      }
    }
    return false;
  };
  findAndToggle(comments.value);
};

const handleCommentSubmit = (content: string) => {
  const newComment: Comment = {
    id: Date.now(),
    user: {
      id: 999,
      name: '我',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    },
    content,
    timeAgo: '刚刚',
    likes: 0,
    isLiked: false
  };
  comments.value.unshift(newComment);
  
  uni.showToast({
    title: '评论成功',
    icon: 'success'
  });
};

const showEmojiPicker = () => {
  uni.showToast({
    title: 'Emoji选择器开发中',
    icon: 'none'
  });
};

const handleReport = (reason: string) => {
  uni.showToast({
    title: '举报成功',
    icon: 'success'
  });
  showReportModal.value = false;
};

const handleBack = () => {
  uni.navigateBack();
};
</script>

<style scoped lang="scss">
.post-detail-container {
  background-color: #f3f4f6;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-scroll {
  flex: 1;
  background-color: #f3f4f6;
}

.post-main {
  background-color: #fff;
  margin: 16rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1F2937;
}

.meta-info {
  font-size: 24rpx;
  color: #9CA3AF;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.follow-btn {
  font-size: 26rpx;
  background-color: #8B5CF6;
  color: white;
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  margin: 0;
  
  &:active {
    background-color: #7c4ee4;
  }
}

.post-body {
  margin-bottom: 24rpx;
}

.content-text {
  font-size: 32rpx;
  color: #374151;
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
}

.image-grid {
  display: grid;
  gap: 12rpx;
  
  &.grid-1 { grid-template-columns: 1fr; }
  &.grid-2 { grid-template-columns: 1fr 1fr; }
  &.grid-3 { grid-template-columns: 1fr 1fr 1fr; }
  &.grid-4 { grid-template-columns: 1fr 1fr; }
  
  .grid-image {
    width: 100%;
    height: 300rpx;
    border-radius: 16rpx;
    background-color: #f3f4f6;
  }
  
  &.grid-1 .grid-image {
    height: 400rpx;
  }
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.topic-tags {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
  flex: 1;
  margin-right: 20rpx;
}

.topic-tag {
  background-color: #EEF2FF;
  color: #6366F1;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.social-stats {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #6B7280;
  font-size: 28rpx;
}

.comments-section {
  background-color: #fff;
  margin: 0 16rpx 16rpx;
  border-radius: 24rpx;
  padding: 32rpx;
}

.comments-header {
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1F2937;
}
</style> 