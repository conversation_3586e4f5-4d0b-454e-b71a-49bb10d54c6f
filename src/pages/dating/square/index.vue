<template>
  <view class="square-container">
    <z-paging ref="paging" v-model="postList" @query="queryList">
      <template #top>
        <CustomNavBar title="广场" />
      </template>
      <view class="post-list">
        <PostItem v-for="post in postList" :key="post.id" :post="post" @on-action="handleAction" />
      </view>
    </z-paging>
    <!-- 发布按钮 -->
    <view class="publish-fab" @click="goToPublish">
      <uni-icons type="plusempty" size="24" color="#FFFFFF"></uni-icons>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PostItem from '@/components/dating/square/PostItem.vue';
import CustomNavBar from '@/components/CustomNavBar.vue';

// Mock Post Data Interface
interface User {
  id: number;
  name: string;
  avatar: string;
}

interface Post {
  id: number;
  user: User;
  content: string;
  images?: string[];
  video?: string;
  topics: string[];
  likes: number;
  comments: number;
  isLiked: boolean;
  distance: string;
  timeAgo: string;
}

const paging = ref<any>(null);
const postList = ref<Post[]>([]);

const queryList = (pageNo: number, pageSize: number) => {
  // 模拟网络请求
  setTimeout(() => {
    const newPosts = generateMockPosts(pageNo, pageSize);
    paging.value.complete(newPosts);
  }, 500);
};

const goToPublish = () => {
  uni.navigateTo({ url: '/pages/dating/square/publish' });
};

const handleAction = (action: { type: string; payload: any }) => {
  console.log('Action:', action.type, 'Payload:', action.payload);
  // Handle actions like 'like', 'comment', 'follow', 'view-profile', etc.
  if (action.type === 'view-post') {
    uni.navigateTo({ url: `/pages/dating/square/detail?id=${action.payload.id}` });
  }
};

// Mock Data Generation
const generateMockPosts = (page: number, count: number): Post[] => {
  const users: User[] = [
    { id: 1, name: '思思', avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
    { id: 2, name: '小甜豆', avatar: 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
    { id: 3, name: '一只小耳朵', avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
  ];
  const sampleImages = [
    'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=60',
    'https://images.unsplash.com/photo-1554034483-04fda0d3507b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=60',
    'https://images.unsplash.com/photo-1562620459-5f75c53b1d06?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=60',
    'https://images.unsplash.com/photo-1519751138087-5bf79df62d5b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=60',
  ];
  const posts: Post[] = [];
  for (let i = 0; i < count; i++) {
    const id = (page - 1) * count + i + 1;
    posts.push({
      id,
      user: users[id % users.length],
      content: '今天天气真好，出来爬山，有人一起吗？欢迎来偶遇哦～',
      images: sampleImages.slice(0, Math.floor(Math.random() * 5)),
      topics: ['#周末去哪儿', '#户外运动'],
      likes: Math.floor(Math.random() * 200),
      comments: Math.floor(Math.random() * 50),
      isLiked: Math.random() > 0.5,
      distance: `${(Math.random() * 10).toFixed(1)}km`,
      timeAgo: `${Math.floor(Math.random() * 5) + 1}小时前`,
    });
  }
  return posts;
};
</script>

<style scoped lang="scss">
.square-container {
  background-color: #f3f4f6;
  height: 100vh;
}

.post-list {
  padding: 0 16rpx;
}

.publish-fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(139, 92, 246, 0.3);
  z-index: 10;
  transition: transform 0.2s ease-in-out;

  &:active {
    transform: scale(0.95);
  }
}
</style> 