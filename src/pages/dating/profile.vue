<template>
  <view class="profile-container">
    <CustomNavBar title="我的主页" :is-opacity="true" />
    
    <!-- 个人信息卡片 -->
    <view class="profile-card">
      <view class="profile-bg">
        <image class="bg-image" src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" mode="aspectFill" />
        <view class="bg-overlay"></view>
      </view>
      
      <view class="profile-content">
        <view class="avatar-section">
          <image
            class="avatar"
            src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
            @click="changeAvatar"
            mode="aspectFill"
          />
          <view class="avatar-edit">
            <uni-icons type="camera" size="16" color="#fff"></uni-icons>
          </view>
        </view>
        
        <view class="user-info">
          <text class="nickname">{{ userInfo.nickname }}</text>
          <text class="user-desc">{{ userInfo.description }}</text>
          <view class="user-tags">
            <text class="age-tag">{{ userInfo.age }}岁</text>
            <text class="location-tag">{{ userInfo.location }}</text>
          </view>
        </view>
        
        <view class="edit-profile-btn" @click="editProfile">
          <uni-icons type="compose" size="16" color="#8B5CF6"></uni-icons>
          <text>编辑资料</text>
        </view>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-card">
      <view class="stat-item" @click="navigateTo('/pages/dating/likes')">
        <text class="stat-value">{{ userStats.likes }}</text>
        <text class="stat-label">获赞</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item" @click="navigateTo('/pages/dating/following')">
        <text class="stat-value">{{ userStats.following }}</text>
        <text class="stat-label">关注</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item" @click="navigateTo('/pages/dating/followers')">
        <text class="stat-value">{{ userStats.followers }}</text>
        <text class="stat-label">粉丝</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item" @click="navigateTo('/pages/dating/square/index?tab=my')">
        <text class="stat-value">{{ userStats.posts }}</text>
        <text class="stat-label">动态</text>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="navigateTo('/pages/dating/square/index?tab=my')">
          <view class="menu-icon">
            <uni-icons type="chatboxes" size="24" color="#8B5CF6"></uni-icons>
          </view>
          <view class="menu-content">
            <text class="menu-title">我的动态</text>
            <text class="menu-desc">查看已发布的动态</text>
          </view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
        
        <view class="menu-item" @click="navigateTo('/pages/dating/visitors')">
          <view class="menu-icon">
            <uni-icons type="eye" size="24" color="#10B981"></uni-icons>
          </view>
          <view class="menu-content">
            <text class="menu-title">访客记录</text>
            <text class="menu-desc">谁看过我的资料</text>
          </view>
          <view class="menu-badge">{{ visitorCount }}</view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
        
        <view class="menu-item" @click="navigateTo('/pages/dating/favorites')">
          <view class="menu-icon">
            <uni-icons type="heart" size="24" color="#EF4444"></uni-icons>
          </view>
          <view class="menu-content">
            <text class="menu-title">我的收藏</text>
            <text class="menu-desc">收藏的动态和用户</text>
          </view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
      </view>
      
      <view class="menu-group">
        <view class="menu-item" @click="navigateTo('/pages/dating/settings')">
          <view class="menu-icon">
            <uni-icons type="gear" size="24" color="#6B7280"></uni-icons>
          </view>
          <view class="menu-content">
            <text class="menu-title">偏好设置</text>
            <text class="menu-desc">匹配偏好和隐私设置</text>
          </view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
        
        <view class="menu-item" @click="navigateTo('/pages/dating/help')">
          <view class="menu-icon">
            <uni-icons type="help" size="24" color="#F59E0B"></uni-icons>
          </view>
          <view class="menu-content">
            <text class="menu-title">帮助中心</text>
            <text class="menu-desc">使用帮助和常见问题</text>
          </view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CustomNavBar from "@/components/CustomNavBar.vue";

// 用户信息
const userInfo = ref({
  nickname: '小星星',
  description: '热爱生活，喜欢旅行和摄影 📸',
  age: 25,
  location: '杭州'
});

// 用户统计数据
const userStats = ref({
  likes: 1024,
  following: 512,
  followers: 256,
  posts: 128
});

// 访客数量
const visitorCount = ref(8);

const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      // 处理头像上传
      uni.showToast({
        title: '头像上传成功',
        icon: 'success'
      });
    },
  });
};

const editProfile = () => {
  uni.navigateTo({ 
    url: '/pages/dating/edit-profile' 
  });
};

const navigateTo = (url: string) => {
  uni.navigateTo({ url });
};
</script>

<style scoped lang="scss">
.profile-container {
  background-color: #f3f4f6;
  min-height: 100vh;
}

.profile-card {
  position: relative;
  margin: 24rpx;
  border-radius: 32rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
}

.profile-bg {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.bg-image {
  width: 100%;
  height: 100%;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 100%);
}

.profile-content {
  position: relative;
  padding: 40rpx 32rpx 32rpx;
  margin-top: -80rpx;
  z-index: 2;
}

.avatar-section {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 8rpx solid #fff;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
}

.avatar-edit {
  position: absolute;
  bottom: 8rpx;
  right: calc(50% - 80rpx - 16rpx);
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(139, 92, 246, 0.3);
}

.user-info {
  text-align: center;
  margin-bottom: 32rpx;
}

.nickname {
  font-size: 40rpx;
  font-weight: 700;
  color: #1F2937;
  margin-bottom: 12rpx;
  display: block;
}

.user-desc {
  font-size: 28rpx;
  color: #6B7280;
  line-height: 1.4;
  margin-bottom: 20rpx;
  display: block;
}

.user-tags {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

.age-tag, .location-tag {
  background-color: #EEF2FF;
  color: #6366F1;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.edit-profile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  background-color: #F8FAFC;
  border: 2rpx solid #E2E8F0;
  color: #8B5CF6;
  padding: 20rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:active {
    background-color: #EEF2FF;
    border-color: #8B5CF6;
    transform: scale(0.98);
  }
}

.stats-card {
  display: flex;
  background-color: #fff;
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  padding: 32rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #F3F4F6;
  margin: auto 0;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #9CA3AF;
  font-weight: 500;
}

.menu-section {
  padding: 0 24rpx;
}

.menu-group {
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #F8FAFC;
  }
}

.menu-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  background-color: #F8FAFC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 6rpx;
  display: block;
}

.menu-desc {
  font-size: 24rpx;
  color: #9CA3AF;
}

.menu-badge {
  background: linear-gradient(135deg, #EF4444 0%, #F87171 100%);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  font-weight: 600;
  min-width: 32rpx;
  text-align: center;
}
</style>
