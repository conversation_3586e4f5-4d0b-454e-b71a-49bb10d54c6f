// import * as qiniu from 'qiniu'
import { toast } from '@/utils'
import { getUploadToken } from '@/api/foo'
interface UploadOptions {
  prefix?: string // 改为目录前缀，默认'images/'
  mimeTypes?: string[] // 允许的MIME类型
  maxSize?: number // 最大文件大小（MB）
  onProgress?: (percent: number) => void
}

export const putFile = async (filePath: string, options: UploadOptions = {}) => {
  const {
    prefix = 'images/',
    mimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'video/mp4'],
    maxSize = 50,
    onProgress,
  } = options

  // 新增路径处理逻辑
  const now = new Date()
  const datePath = [
    now.getFullYear(),
    String(now.getMonth() + 1).padStart(2, '0'),
    String(now.getDate()).padStart(2, '0'),
  ].join('-')

  const getFileNameWithExt = () => {
    // 参数校验
    const originalExt = filePath.split('.').pop()?.toLowerCase() || ''

    const mimeExtMap: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
      'video/mp4': 'mp4',
      'video/quicktime': 'mov',
    }

    // 生成唯一字符串
    const uniqueString = getUniqueImageName()
    return `${uniqueString}.${originalExt}`
  }

  // 校验文件类型
  // if (!mimeTypes.includes(file.type)) {
  //   throw new Error(`不支持的文件类型: ${file.type}`)
  // }

  // 校验文件大小
  // if (file.size > maxSize * 1024 * 1024) {
  //   throw new Error(`文件大小不能超过 ${maxSize}MB`)
  // }

  try {
    const { code, msg, data } = await getUploadToken()
    if (code !== 0) {
      toast(msg)
      throw new Error(msg)
    }

    // 生成最终文件名
    const fileName = getFileNameWithExt()
    const finalKey = `${prefix}${datePath}/${fileName}`

    return new Promise<string>((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: 'https://up-z1.qiniup.com',
        filePath: filePath,
        name: 'file',
        formData: {
          token: data.token,
          key: finalKey,
        },
        success: (res) => {
          if (res.statusCode === 200) {
            // const result = JSON.parse(res.data)
            resolve(finalKey)
          } else {
            reject(new Error(`[${res.statusCode}]上传失败`))
          }
        },
        fail: reject,
      })

      // 添加进度监听
      uploadTask.onProgressUpdate((e) => {
        onProgress?.(e.progress)
      })
    })
  } catch (error) {
    toast(error.message || '上传失败')
    throw error
  }

  // 创建上传实例
}

// 生成唯一图片名称
export const getUniqueImageName = () => {
  return `${Math.random().toString(36).substring(2, 8)}`
}

export default putFile
