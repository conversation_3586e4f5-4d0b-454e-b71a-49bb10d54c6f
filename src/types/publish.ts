// 房产发布相关类型定义

// 租房发布数据
export interface RentPublishData {
  rentType: '整套出租' | '合租出租';
  city: string;
  community: string;
  building: string;
  totalFloor: number;
  currentFloor: number;
  area: string;
  houseType: string;
  orientation: string;
  monthlyRent: string;
  paymentMethod: string;
  includedFees: string;
  images: string[];
  description: string;
}

// 二手房发布数据
export interface SecondHandHousePublishData {
  community: string;
  houseType: string;
  area: string;
  price: string;
  floor: string;
  orientation: string;
  decoration: string;
  images: string[];
  description: string;
}

// 新房发布数据
export interface NewHousePublishData {
  projectName: string;
  address: string;
  developer: string;
  averagePrice: string;
  houseTypes: string;
  deliveryTime: string;
  selectedTags: string[];
  images: string[];
  description: string;
}

// 商业地产发布数据
export interface CommercialPublishData {
  propertyType: 'rent' | 'sale';
  propertyName: string;
  area: string;
  price: string;
  floor: string;
  images: string[];
  description: string;
}

// 发布状态
export type PublishStatus = 'published' | 'draft' | 'offline';

// 房产管理项目
export interface HouseManageItem {
  id: string;
  title: string;
  location: string;
  price: number;
  houseType: string;
  area: number;
  views: number;
  publishTime: string;
  status: PublishStatus;
  type: 'rent' | 'second-hand' | 'new-house' | 'commercial';
}

// 发布类型配置
export interface PublishTypeConfig {
  type: string;
  name: string;
  icon: string;
  path: string;
}

// 分类管理配置
export interface CategoryManageConfig {
  type: string;
  name: string;
  icon: string;
  path: string;
  count: number;
} 