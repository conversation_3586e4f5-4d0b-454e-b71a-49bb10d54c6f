/**
 * 地区相关常量定义
 */

// 城市列表（可根据实际业务需求扩展）
export const CITY_LIST = [
    "北京",
    "上海",
    "广州",
    "深圳",
    "杭州",
    "南京",
    "武汉",
    "成都",
    "重庆",
    "天津",
    "苏州",
    "西安",
    "沈阳",
    "青岛",
    "宁波",
    "无锡",
    "郑州",
    "长沙",
    "大连",
    "厦门",
    "济南",
    "哈尔滨",
    "长春",
    "福州",
    "石家庄",
    "合肥",
    "昆明",
    "南昌",
    "太原",
    "贵阳"
];

// 省市区三级联动数据结构
export interface AreaItem {
    label: string;
    value: string;
    children?: AreaItem[];
}

// 示例省市区数据（实际项目中可以从接口获取或使用完整的省市区数据）
export const PROVINCE_CITY_AREA: AreaItem[] = [
    {
        label: "北京市",
        value: "110000",
        children: [
            {
                label: "北京市",
                value: "110100",
                children: [
                    { label: "东城区", value: "110101" },
                    { label: "西城区", value: "110102" },
                    { label: "朝阳区", value: "110105" },
                    { label: "丰台区", value: "110106" },
                    { label: "石景山区", value: "110107" },
                    { label: "海淀区", value: "110108" },
                    { label: "门头沟区", value: "110109" },
                    { label: "房山区", value: "110111" },
                    { label: "通州区", value: "110112" },
                    { label: "顺义区", value: "110113" },
                    { label: "昌平区", value: "110114" },
                    { label: "大兴区", value: "110115" },
                    { label: "怀柔区", value: "110116" },
                    { label: "平谷区", value: "110117" },
                    { label: "密云区", value: "110118" },
                    { label: "延庆区", value: "110119" }
                ]
            }
        ]
    },
    {
        label: "上海市",
        value: "310000",
        children: [
            {
                label: "上海市",
                value: "310100",
                children: [
                    { label: "黄浦区", value: "310101" },
                    { label: "徐汇区", value: "310104" },
                    { label: "长宁区", value: "310105" },
                    { label: "静安区", value: "310106" },
                    { label: "普陀区", value: "310107" },
                    { label: "虹口区", value: "310109" },
                    { label: "杨浦区", value: "310110" },
                    { label: "闵行区", value: "310112" },
                    { label: "宝山区", value: "310113" },
                    { label: "嘉定区", value: "310114" },
                    { label: "浦东新区", value: "310115" },
                    { label: "金山区", value: "310116" },
                    { label: "松江区", value: "310117" },
                    { label: "青浦区", value: "310118" },
                    { label: "奉贤区", value: "310120" },
                    { label: "崇明区", value: "310151" }
                ]
            }
        ]
    },
    {
        label: "广东省",
        value: "440000",
        children: [
            {
                label: "广州市",
                value: "440100",
                children: [
                    { label: "荔湾区", value: "440103" },
                    { label: "越秀区", value: "440104" },
                    { label: "海珠区", value: "440105" },
                    { label: "天河区", value: "440106" },
                    { label: "白云区", value: "440111" },
                    { label: "黄埔区", value: "440112" },
                    { label: "番禺区", value: "440113" },
                    { label: "花都区", value: "440114" },
                    { label: "南沙区", value: "440115" },
                    { label: "从化区", value: "440117" },
                    { label: "增城区", value: "440118" }
                ]
            },
            {
                label: "深圳市",
                value: "440300",
                children: [
                    { label: "罗湖区", value: "440303" },
                    { label: "福田区", value: "440304" },
                    { label: "南山区", value: "440305" },
                    { label: "宝安区", value: "440306" },
                    { label: "龙岗区", value: "440307" },
                    { label: "盐田区", value: "440308" },
                    { label: "龙华区", value: "440309" },
                    { label: "坪山区", value: "440310" },
                    { label: "光明区", value: "440311" }
                ]
            }
        ]
    },
    {
        label: "浙江省",
        value: "330000",
        children: [
            {
                label: "杭州市",
                value: "330100",
                children: [
                    { label: "上城区", value: "330102" },
                    { label: "下城区", value: "330103" },
                    { label: "江干区", value: "330104" },
                    { label: "拱墅区", value: "330105" },
                    { label: "西湖区", value: "330106" },
                    { label: "滨江区", value: "330108" },
                    { label: "萧山区", value: "330109" },
                    { label: "余杭区", value: "330110" },
                    { label: "富阳区", value: "330111" },
                    { label: "临安区", value: "330112" }
                ]
            }
        ]
    }
];

// 常用商圈数据（可根据不同城市动态加载）
export const BUSINESS_AREAS = {
    "北京": [
        "国贸CBD",
        "中关村",
        "望京",
        "三里屯",
        "西单",
        "王府井",
        "燕莎",
        "亚运村",
        "金融街",
        "奥体中心"
    ],
    "上海": [
        "陆家嘴",
        "徐家汇",
        "淮海路",
        "南京路",
        "静安寺",
        "人民广场",
        "五角场",
        "中山公园",
        "打浦桥",
        "虹桥"
    ],
    "广州": [
        "天河城",
        "北京路",
        "上下九",
        "珠江新城",
        "体育西",
        "公园前",
        "东风东路",
        "江南西",
        "客村",
        "岗顶"
    ],
    "深圳": [
        "福田中心区",
        "南山科技园",
        "罗湖商业区",
        "华强北",
        "宝安中心",
        "龙华新区",
        "坂田",
        "西乡",
        "布吉",
        "观澜"
    ]
};

// 地铁线路数据（示例）
export const SUBWAY_LINES = {
    "北京": [
        "1号线", "2号线", "4号线", "5号线", "6号线", "7号线", "8号线", "9号线", "10号线",
        "13号线", "14号线", "15号线", "16号线", "八通线", "昌平线", "房山线", "亦庄线"
    ],
    "上海": [
        "1号线", "2号线", "3号线", "4号线", "5号线", "6号线", "7号线", "8号线", "9号线",
        "10号线", "11号线", "12号线", "13号线", "14号线", "15号线", "16号线", "17号线"
    ],
    "广州": [
        "1号线", "2号线", "3号线", "4号线", "5号线", "6号线", "7号线", "8号线", "9号线",
        "13号线", "14号线", "21号线", "APM线", "广佛线"
    ],
    "深圳": [
        "1号线", "2号线", "3号线", "4号线", "5号线", "7号线", "9号线", "11号线"
    ]
}; 