/**
 * 房产相关常量定义
 */

// 房屋户型 - 三列选择器
export const HOUSE_TYPE_RANGE = [
    ["1室", "2室", "3室", "4室", "5室", "6室"],
    ["0厅", "1厅", "2厅", "3厅", "4厅"],
    ["1卫", "2卫", "3卫", "4卫"]
];

// 房屋户型选项（用于搜索和过滤）
export const HOUSE_TYPE_OPTIONS = [
    // 室选项
    { label: "1室", value: "1_room" },
    { label: "2室", value: "2_room" },
    { label: "3室", value: "3_room" },
    { label: "4室", value: "4_room" },
    { label: "5室", value: "5_room" },
    { label: "6室", value: "6_room" },
    // 厅选项
    { label: "0厅", value: "0_hall" },
    { label: "1厅", value: "1_hall" },
    { label: "2厅", value: "2_hall" },
    { label: "3厅", value: "3_hall" },
    { label: "4厅", value: "4_hall" },
    // 卫选项
    { label: "1卫", value: "1_bathroom" },
    { label: "2卫", value: "2_bathroom" },
    { label: "3卫", value: "3_bathroom" },
    { label: "4卫", value: "4_bathroom" }
];

// 常用户型组合（用于快速搜索）
export const COMMON_HOUSE_TYPES = [
    { label: "1室1厅1卫", value: "1_1_1", room: 1, hall: 1, bathroom: 1 },
    { label: "1室1厅2卫", value: "1_1_2", room: 1, hall: 1, bathroom: 2 },
    { label: "2室1厅1卫", value: "2_1_1", room: 2, hall: 1, bathroom: 1 },
    { label: "2室1厅2卫", value: "2_1_2", room: 2, hall: 1, bathroom: 2 },
    { label: "2室2厅1卫", value: "2_2_1", room: 2, hall: 2, bathroom: 1 },
    { label: "2室2厅2卫", value: "2_2_2", room: 2, hall: 2, bathroom: 2 },
    { label: "3室1厅1卫", value: "3_1_1", room: 3, hall: 1, bathroom: 1 },
    { label: "3室1厅2卫", value: "3_1_2", room: 3, hall: 1, bathroom: 2 },
    { label: "3室2厅1卫", value: "3_2_1", room: 3, hall: 2, bathroom: 1 },
    { label: "3室2厅2卫", value: "3_2_2", room: 3, hall: 2, bathroom: 2 },
    { label: "4室2厅2卫", value: "4_2_2", room: 4, hall: 2, bathroom: 2 },
    { label: "4室2厅3卫", value: "4_2_3", room: 4, hall: 2, bathroom: 3 },
    { label: "5室2厅3卫", value: "5_2_3", room: 5, hall: 2, bathroom: 3 }
];

// 房屋朝向选项
export const ORIENTATION_OPTIONS = [
    { label: "南", value: "south" },
    { label: "北", value: "north" },
    { label: "东", value: "east" },
    { label: "西", value: "west" },
    { label: "南北", value: "south_north" },
    { label: "东南", value: "southeast" },
    { label: "西南", value: "southwest" },
    { label: "东北", value: "northeast" },
    { label: "西北", value: "northwest" }
];

// 付款方式选项
export const PAYMENT_METHOD_OPTIONS = [
    { label: "付一押一", value: "pay1_deposit1" },
    { label: "付二押一", value: "pay2_deposit1" },
    { label: "付三押一", value: "pay3_deposit1" },
    { label: "月付", value: "monthly" },
    { label: "季付", value: "quarterly" },
    { label: "半年付", value: "semi_annual" },
    { label: "年付", value: "annual" }
];

// 新房付款方式
export const NEW_HOUSE_PAYMENT_OPTIONS = [
    { label: "一次性付款", value: "full_payment" },
    { label: "按揭贷款", value: "mortgage" },
    { label: "分期付款", value: "installment" },
    { label: "公积金贷款", value: "provident_fund" },
    { label: "组合贷款", value: "combination_loan" }
];

// 包含费用选项
export const INCLUDED_FEES_OPTIONS = [
    { label: "水费", value: "water" },
    { label: "电费", value: "electricity" },
    { label: "燃气费", value: "gas" },
    { label: "网费", value: "internet" },
    { label: "物业费", value: "property" },
    { label: "暖气费", value: "heating" },
    { label: "停车费", value: "parking" },
    { label: "有线电视费", value: "cable_tv" }
];

// 房源配套设施选项
export const FACILITIES_OPTIONS = [
    {
        type: "appliances",
        title: "家用电器",
        items: [
            { label: "空调", value: "air_conditioner" },
            { label: "冰箱", value: "refrigerator" },
            { label: "洗衣机", value: "washing_machine" },
            { label: "电视", value: "tv" },
            { label: "热水器", value: "water_heater" },
            { label: "微波炉", value: "microwave" },
            { label: "油烟机", value: "range_hood" },
            { label: "洗碗机", value: "dishwasher" }
        ]
    },
    {
        type: "furniture",
        title: "家具设施",
        items: [
            { label: "床", value: "bed" },
            { label: "衣柜", value: "wardrobe" },
            { label: "沙发", value: "sofa" },
            { label: "餐桌", value: "dining_table" },
            { label: "书桌", value: "desk" },
            { label: "椅子", value: "chair" },
            { label: "茶几", value: "coffee_table" },
            { label: "鞋柜", value: "shoe_cabinet" }
        ]
    },
    {
        type: "network",
        title: "网络设施",
        items: [
            { label: "宽带", value: "broadband" },
            { label: "WiFi", value: "wifi" },
            { label: "有线电视", value: "cable_tv" }
        ]
    },
    {
        type: "security",
        title: "安全设施",
        items: [
            { label: "门禁", value: "access_control" },
            { label: "监控", value: "surveillance" },
            { label: "安全门", value: "security_door" },
            { label: "保安", value: "security_guard" }
        ]
    }
];

// 房源亮点标签选项
export const HOUSE_TAGS_OPTIONS = [
    { label: "近地铁", value: "near_subway" },
    { label: "近商圈", value: "near_business" },
    { label: "近学校", value: "near_school" },
    { label: "近医院", value: "near_hospital" },
    { label: "交通便利", value: "convenient_transport" },
    { label: "采光好", value: "good_lighting" },
    { label: "朝南", value: "south_facing" },
    { label: "精装修", value: "fine_decoration" },
    { label: "新装修", value: "newly_decorated" },
    { label: "拎包入住", value: "move_in_ready" },
    { label: "独立卫生间", value: "private_bathroom" },
    { label: "独立阳台", value: "private_balcony" },
    { label: "电梯房", value: "elevator" },
    { label: "停车位", value: "parking_space" },
    { label: "宠物友好", value: "pet_friendly" },
    { label: "花园景观", value: "garden_view" },
    { label: "江景房", value: "river_view" },
    { label: "高层视野", value: "high_floor_view" }
];

// 新房项目亮点标签
export const PROJECT_TAGS_OPTIONS = [
    { label: "地铁房", value: "subway_property" },
    { label: "学区房", value: "school_district" },
    { label: "现房", value: "ready_house" },
    { label: "期房", value: "pre_sale_house" },
    { label: "公园景观", value: "park_view" },
    { label: "商业配套", value: "commercial_facilities" },
    { label: "医疗配套", value: "medical_facilities" },
    { label: "精装修", value: "fine_decoration" },
    { label: "毛坯", value: "rough_house" },
    { label: "低密度", value: "low_density" },
    { label: "高层", value: "high_rise" },
    { label: "洋房", value: "garden_house" },
    { label: "别墅", value: "villa" },
    { label: "复式", value: "duplex" },
    { label: "loft", value: "loft" }
];

// 周边配套设施选项（新房/商铺通用）
export const SURROUNDING_FACILITIES_OPTIONS = [
    {
        type: "transport",
        title: "交通设施",
        items: [
            { label: "地铁", value: "subway" },
            { label: "公交", value: "bus" },
            { label: "高速入口", value: "highway" },
            { label: "停车场", value: "parking" },
            { label: "机场", value: "airport" },
            { label: "火车站", value: "train_station" }
        ]
    },
    {
        type: "education",
        title: "教育配套",
        items: [
            { label: "幼儿园", value: "kindergarten" },
            { label: "小学", value: "primary_school" },
            { label: "中学", value: "middle_school" },
            { label: "高中", value: "high_school" },
            { label: "大学", value: "university" },
            { label: "培训机构", value: "training_center" }
        ]
    },
    {
        type: "medical",
        title: "医疗配套",
        items: [
            { label: "三甲医院", value: "class_a_hospital" },
            { label: "社区医院", value: "community_hospital" },
            { label: "诊所", value: "clinic" },
            { label: "药店", value: "pharmacy" },
            { label: "体检中心", value: "health_check_center" }
        ]
    },
    {
        type: "commercial",
        title: "商业配套",
        items: [
            { label: "大型商场", value: "shopping_mall" },
            { label: "超市", value: "supermarket" },
            { label: "便利店", value: "convenience_store" },
            { label: "银行", value: "bank" },
            { label: "餐饮", value: "restaurant" },
            { label: "农贸市场", value: "farmers_market" }
        ]
    },
    {
        type: "life",
        title: "生活配套",
        items: [
            { label: "公园", value: "park" },
            { label: "健身房", value: "gym" },
            { label: "游泳池", value: "swimming_pool" },
            { label: "社区服务", value: "community_service" },
            { label: "邮局", value: "post_office" },
            { label: "美容美发", value: "beauty_salon" }
        ]
    }
];

// 产权年限选项
export const PROPERTY_YEARS_OPTIONS = [
    { label: "40年", value: "40" },
    { label: "50年", value: "50" },
    { label: "70年", value: "70" }
];

// 销售状态选项
export const SALES_STATUS_OPTIONS = [
    { label: "在售", value: "on_sale" },
    { label: "即将开盘", value: "coming_soon" },
    { label: "售罄", value: "sold_out" },
    { label: "暂停销售", value: "suspended" },
    { label: "尾盘", value: "final_phase" }
];

// 建筑类型选项
export const BUILDING_TYPE_OPTIONS = [
    { label: "高层", value: "high_rise" },
    { label: "小高层", value: "mid_rise" },
    { label: "洋房", value: "garden_house" },
    { label: "别墅", value: "villa" },
    { label: "商住两用", value: "commercial_residential" },
    { label: "公寓", value: "apartment" },
    { label: "复式", value: "duplex" },
    { label: "loft", value: "loft" }
];

// 装修状况选项
export const DECORATION_OPTIONS = [
    { label: "毛坯", value: "rough" },
    { label: "简装", value: "simple" },
    { label: "中装", value: "medium" },
    { label: "精装", value: "fine" },
    { label: "豪装", value: "luxury" }
];

// 商业地产类型选项
export const COMMERCIAL_PROPERTY_TYPE_OPTIONS = [
    { label: "商铺", value: "shop" },
    { label: "写字楼", value: "office" },
    { label: "厂房", value: "factory" },
    { label: "仓库", value: "warehouse" },
    { label: "土地", value: "land" },
    { label: "车位", value: "parking_lot" }
];

// 商铺经营范围选项
export const BUSINESS_SCOPE_OPTIONS = [
    { label: "餐饮", value: "catering" },
    { label: "零售", value: "retail" },
    { label: "服装", value: "clothing" },
    { label: "美容美发", value: "beauty" },
    { label: "教育培训", value: "education" },
    { label: "医疗保健", value: "healthcare" },
    { label: "金融服务", value: "finance" },
    { label: "汽车服务", value: "automotive" },
    { label: "娱乐休闲", value: "entertainment" },
    { label: "其他", value: "other" }
];

// 建设年代选项（2000-2030）
export const BUILD_YEAR_OPTIONS = (() => {
    const years = [];
    for (let year = 2030; year >= 2000; year--) {
        years.push({ label: `${year}年`, value: year.toString() });
    }
    return years;
})();

// 交房时间选项（按季度）
export const DELIVERY_TIME_OPTIONS = (() => {
    const options = [];
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year <= currentYear + 5; year++) {
        for (let quarter = 1; quarter <= 4; quarter++) {
            options.push({
                label: `${year}年第${quarter}季度`,
                value: `${year}_Q${quarter}`
            });
        }
    }
    return options;
})();

// 开盘时间选项（按月份）
export const OPENING_TIME_OPTIONS = (() => {
    const options = [];
    const currentYear = new Date().getFullYear();
    const months = [
        "1月", "2月", "3月", "4月", "5月", "6月",
        "7月", "8月", "9月", "10月", "11月", "12月"
    ];

    for (let year = currentYear - 1; year <= currentYear + 3; year++) {
        months.forEach((month, index) => {
            options.push({
                label: `${year}年${month}`,
                value: `${year}_${(index + 1).toString().padStart(2, '0')}`
            });
        });
    }
    return options;
})(); 