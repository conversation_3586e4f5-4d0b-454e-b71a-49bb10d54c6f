<template>
  <view class="dating-container bg-card mb-20rpx">
    <view
      class="section-header flex justify-between items-center px-20rpx py-20rpx"
    >
      <text class="text-32rpx font-bold">交友推荐</text>
      <view class="flex items-center" @tap="navigateTo('/pages/dating/index')">
        <text class="text-26rpx text-grey">全部</text>
        <text class="i-carbon-chevron-right text-grey"></text>
      </view>
    </view>
    <view class="dating-list px-20rpx pb-20rpx">
      <scroll-view scroll-x class="dating-scroll" show-scrollbar="false">
        <view class="flex">
          <view
            v-for="(profile, index) in datingProfiles"
            :key="index"
            class="dating-item mr-20rpx"
            @tap="navigateTo(profile.link)"
          >
            <view class="dating-card">
              <image
                :src="profile.avatar"
                mode="aspectFill"
                class="dating-avatar"
              />
              <view class="dating-info p-16rpx">
                <view class="flex items-center">
                  <text class="dating-name text-30rpx font-bold">{{
                    profile.name
                  }}</text>
                  <text class="dating-age text-26rpx ml-10rpx"
                    >{{ profile.age }}岁</text
                  >
                </view>
                <view class="dating-tags flex flex-wrap mt-10rpx">
                  <view
                    v-for="(tag, tagIndex) in profile.tags"
                    :key="tagIndex"
                    class="dating-tag mr-10rpx mb-10rpx"
                  >
                    {{ tag }}
                  </view>
                </view>
                <view class="flex items-center mt-10rpx">
                  <text
                    class="i-carbon-location text-grey text-24rpx mr-6rpx"
                  ></text>
                  <text class="text-grey text-26rpx">{{
                    profile.location
                  }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 导航方法
const navigateTo = (path: string) => {
  if (!path) return;
  uni.navigateTo({
    url: path,
  });
};

// 交友推荐列表
const datingProfiles = [
  {
    name: "小雨",
    age: 26,
    avatar: "https://picsum.photos/seed/dating1/300/400",
    tags: ["教师", "爱旅行", "温柔"],
    location: "海淀区",
    link: "/pages/dating/profile?id=1",
  },
  {
    name: "阳阳",
    age: 28,
    avatar: "https://picsum.photos/seed/dating2/300/400",
    tags: ["程序员", "健身", "幽默"],
    location: "朝阳区",
    link: "/pages/dating/profile?id=2",
  },
  {
    name: "小文",
    age: 25,
    avatar: "https://picsum.photos/seed/dating3/300/400",
    tags: ["设计师", "爱好美食", "开朗"],
    location: "丰台区",
    link: "/pages/dating/profile?id=3",
  },
  {
    name: "小李",
    age: 29,
    avatar: "https://picsum.photos/seed/dating4/300/400",
    tags: ["医生", "喜欢阅读", "善良"],
    location: "西城区",
    link: "/pages/dating/profile?id=4",
  },
];
</script>

<style scoped>
.dating-scroll {
  white-space: nowrap;
  overflow: visible;
}

.dating-item {
  display: inline-block;
  width: 240rpx;
}

.dating-card {
  border-radius: 16rpx;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s;
}

.dating-card:active {
  transform: scale(0.98);
}

.dating-avatar {
  width: 240rpx;
  height: 320rpx;
  object-fit: cover;
}

.dating-name {
  color: var(--text-base);
}

.dating-age {
  color: var(--text-info);
}

.dating-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: var(--bg-tag);
  color: var(--text-info);
  border-radius: 6rpx;
}
</style>
