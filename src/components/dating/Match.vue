<template>
  <view class="match-container">
    <!-- Loading状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-animation">
        <view class="loading-heart">
          <image src="/static/svg/dating/like.svg" class="heart-icon" />
        </view>
        <view class="loading-text">正在为你寻找匹配...</view>
        <view class="loading-dots">
          <view class="dot" :class="{ active: loadingDotIndex === 0 }"></view>
          <view class="dot" :class="{ active: loadingDotIndex === 1 }"></view>
          <view class="dot" :class="{ active: loadingDotIndex === 2 }"></view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view v-else class="main-content">
      <!-- 自定义导航栏，确保在最顶层 -->
      <view class="custom-nav-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
        <view class="nav-content">
          <view class="nav-icon" @click="goBack">
            <uni-icons type="back" size="22" color="#fff"></uni-icons>
          </view>
          <view class="nav-spacer"></view>
          <view class="nav-icon filter-icon" @click="openFilter">
            <uni-icons type="tune" size="18" color="#fff"></uni-icons>
          </view>
        </view>
      </view>
      
      <view class="card-stack">
        <view
          v-for="(user, userIndex) in visibleUsers"
          :key="`${user.id}-${currentUserIndex}`"
          class="user-card"
          :class="{ 
            'card-animating': user.isAnimating,
            'card-behind': userIndex > 0
          }"
          :style="{
            zIndex: visibleUsers.length - userIndex + 100,
            transform: getCardTransform(userIndex, user.animationType),
            opacity: getCardOpacity(userIndex, user.animationType)
          }"
          @click="goToUserDetail(user)"
        >
          <swiper
            class="card-swiper"
            :indicator-dots="false"
            :autoplay="false"
            :circular="false"
            :current="user.currentImageIndex || 0"
            @change="onSwiperChange($event, user.id)"
          >
            <swiper-item
              v-for="(picture, picIndex) in user.pictures"
              :key="picIndex"
            >
              <image :src="picture" class="user-avatar" mode="aspectFill" />
            </swiper-item>
          </swiper>

          <!-- 只在顶层卡片显示指示器 -->
          <view 
            class="swiper-indicator" 
            v-if="user.pictures.length > 1 && userIndex === 0"
          >
            <view class="indicator-background">
              <view
                v-for="(dot, i) in user.pictures.length"
                :key="i"
                class="indicator-dot"
                :class="{ active: i === (user.currentImageIndex || 0) }"
              ></view>
            </view>
          </view>

          <!-- 只在顶层卡片显示完整信息，后面的卡片不显示文字信息 -->
          <view class="card-overlay" v-if="userIndex === 0">
            <view class="user-info">
              <text class="user-name">{{ user.name }}, {{ user.age }}</text>
              <view class="user-distance">
                <tui-icon name="position" color="#fff" :size="14"></tui-icon>
                <text>{{ user.distance }}km</text>
              </view>
              <view class="user-description" v-if="user.description">
                <text>{{ user.description }}</text>
              </view>
              <view class="user-tags">
                <view v-for="tag in user.tags" :key="tag" class="tag">{{
                  tag
                }}</view>
              </view>
            </view>
            <view class="action-buttons">
              <view
                class="action-btn-wrapper"
                @click.stop="handleAction('dislike')"
              >
                <image src="/static/svg/dating/close.svg" class="action-icon" />
              </view>
              <view
                class="action-btn-wrapper primary"
                @click.stop="handleAction('superlike')"
              >
                <image
                  src="/static/svg/dating/super-like.svg"
                  class="action-icon"
                />
              </view>
              <view class="action-btn-wrapper" @click.stop="handleAction('like')">
                <image src="/static/svg/dating/like.svg" class="action-icon" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 无更多用户提示 -->
      <view v-if="visibleUsers.length === 0 && !isLoading" class="no-more-users">
        <text class="no-more-text">暂无更多用户</text>
        <text class="no-more-desc">调整筛选条件试试</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

interface User {
  id: number;
  name: string;
  age: number;
  distance: number;
  pictures: string[];
  tags: string[];
  description?: string;
  currentImageIndex: number;
  isAnimating?: boolean;
  animationType?: "like" | "dislike" | "superlike";
}

// 状态管理
const isLoading = ref(true);
const loadingDotIndex = ref(0);
const currentUserIndex = ref(0);

// 用户数据 - 初始为空数组，避免闪动
const allUsers = ref<User[]>([]);

// 模拟数据
const mockUsers: User[] = [
  {
    id: 1,
    name: "Kristin",
    age: 23,
    distance: 5,
    pictures: [
      "https://images.unsplash.com/photo-1520466809213-7b9a56adcd45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=880&q=80",
      "https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
      "https://images.unsplash.com/photo-1494790108755-2616b68e3e97?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
    ],
    tags: ["Music", "Anime", "Reading"],
    description: "喜欢音乐和阅读，寻找有趣的灵魂",
    currentImageIndex: 0,
  },
  {
    id: 2,
    name: "Jessica",
    age: 25,
    distance: 2,
    pictures: [
      "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=761&q=80",
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    ],
    tags: ["Travel", "Foodie"],
    description: "环球旅行爱好者，美食探索家",
    currentImageIndex: 0,
  },
  {
    id: 3,
    name: "Amanda",
    age: 21,
    distance: 8,
    pictures: [
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80",
      "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
      "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
    ],
    tags: ["Movies", "Hiking", "Art"],
    description: "电影迷和户外运动爱好者",
    currentImageIndex: 0,
  },
  {
    id: 4,
    name: "Emma",
    age: 26,
    distance: 12,
    pictures: [
      "https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
      "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80",
    ],
    tags: ["Yoga", "Photography"],
    description: "瑜伽教练，摄影爱好者",
    currentImageIndex: 0,
  },
  {
    id: 5,
    name: "Sophie",
    age: 24,
    distance: 6,
    pictures: [
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80",
    ],
    tags: ["Dancing", "Cooking"],
    description: "舞蹈老师，喜欢烹饪",
    currentImageIndex: 0,
  },
  {
    id: 6,
    name: "Lily",
    age: 22,
    distance: 15,
    pictures: [
      "https://images.unsplash.com/photo-1488716820095-cbe80883c496?ixlib=rb-4.0.3&auto=format&fit=crop&w=686&q=80",
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
      "https://images.unsplash.com/photo-1485893226505-9409047665b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=880&q=80",
    ],
    tags: ["Fashion", "Shopping"],
    description: "时尚达人，购物狂",
    currentImageIndex: 0,
  },
];

const visibleUsers = computed(() => {
  if (allUsers.value.length === 0) return [];
  
  // 确保不超出数组范围
  const startIndex = Math.min(currentUserIndex.value, allUsers.value.length - 1);
  const endIndex = Math.min(startIndex + 3, allUsers.value.length);
  
  return allUsers.value.slice(startIndex, endIndex);
});

// 修复图片滑动逻辑
const onSwiperChange = (event: any, userId: number) => {
  const userIndex = allUsers.value.findIndex(user => user.id === userId);
  if (userIndex !== -1) {
    allUsers.value[userIndex].currentImageIndex = event.detail.current;
  }
};

const getCardTransform = (index: number, animationType?: string) => {
  if (animationType === "like") {
    return `translateX(100vw) rotate(20deg) scale(0.8)`;
  }
  if (animationType === "dislike") {
    return `translateX(-100vw) rotate(-20deg) scale(0.8)`;
  }
  if (animationType === "superlike") {
    return `translateY(-100vh) scale(0.8)`;
  }

  if (index === 0) return "translateY(0) scale(1)";
  if (index === 1) return `translateY(-30rpx) scale(0.95)`;
  if (index === 2) return `translateY(-60rpx) scale(0.9)`;
  return `translateY(${index * -40}rpx) scale(${1 - index * 0.05})`;
};

// 新增透明度控制函数
const getCardOpacity = (index: number, animationType?: string) => {
  if (animationType === "like" || animationType === "dislike" || animationType === "superlike") {
    return 1;
  }
  
  if (index === 0) return 1;
  if (index === 1) return 0.8;
  if (index === 2) return 0.6;
  return Math.max(0.4, 1 - index * 0.2);
};

const handleAction = (action: "like" | "dislike" | "superlike") => {
  if (visibleUsers.value.length === 0) return;

  const currentUser = visibleUsers.value[0];
  const userIndex = allUsers.value.findIndex(user => user.id === currentUser.id);
  
  if (userIndex !== -1) {
    allUsers.value[userIndex].isAnimating = true;
    allUsers.value[userIndex].animationType = action;
  }

  // 显示操作反馈
  showActionFeedback(action);

  setTimeout(() => {
    currentUserIndex.value++;
    console.log(`${action}: ${currentUser.name}`);
  }, 300);
};

const showActionFeedback = (action: string) => {
  let message = "";
  switch (action) {
    case "like":
      message = "喜欢 ❤️";
      break;
    case "dislike":
      message = "不喜欢";
      break;
    case "superlike":
      message = "超级喜欢 ⭐";
      break;
  }

  uni.showToast({
    title: message,
    icon: "none",
    duration: 1000,
  });
};

// Loading动画
const startLoadingAnimation = () => {
  const interval = setInterval(() => {
    loadingDotIndex.value = (loadingDotIndex.value + 1) % 3;
  }, 500);
  
  // 模拟数据加载
  setTimeout(() => {
    clearInterval(interval);
    allUsers.value = mockUsers;
    isLoading.value = false;
  }, 2000);
};

// 获取系统信息
const statusBarHeight = ref(44);

// 初始化
onMounted(() => {
  // 获取系统状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 44;
  
  startLoadingAnimation();
});

const goBack = () => {
  uni.navigateBack();
};

const openFilter = () => {
  uni.navigateTo({
    url: "/pages/dating/settings",
  });
};

const goToUserDetail = (user: User) => {
  uni.navigateTo({
    url: `/pages/dating/user-detail?userId=${user.id}`,
  });
};
</script>

<style scoped lang="scss">
.match-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-animation {
  text-align: center;
  color: white;
}

.loading-heart {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 40rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: heartBeat 1.5s ease-in-out infinite;
  
  .heart-icon {
    width: 60rpx;
    height: 60rpx;
    filter: brightness(0) invert(1);
  }
}

.loading-text {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 40rpx;
  opacity: 0.9;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  
  .dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    
    &.active {
      background-color: rgba(255, 255, 255, 0.9);
      transform: scale(1.2);
    }
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.main-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  padding-bottom: 10px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), transparent);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  height: 88rpx;
}

.nav-spacer {
  flex: 1;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
    background-color: rgba(0, 0, 0, 0.7);
  }

  &.filter-icon {
    width: 72rpx;
    height: 72rpx;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
  }
}

.card-stack {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 20rpx;
  
  // 确保卡片不会超出边界
  .user-card:not(:first-child) {
    .user-avatar {
      filter: brightness(0.8);
    }
  }
}

.user-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 24rpx;

  &.card-animating {
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  }

  &.card-behind {
    pointer-events: none;
    
    .card-overlay {
      display: none;
    }
    
    .swiper-indicator {
      display: none;
    }
  }
}

.card-swiper {
  width: 100%;
  height: 100%;
}

.user-avatar {
  width: 100%;
  height: 100%;
}

.swiper-indicator {
  position: absolute;
  top: 58%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;

  .indicator-background {
    background-color: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 12rpx 20rpx;
    border-radius: 30rpx;
    display: flex;
    gap: 12rpx;
    align-items: center;
  }

  .indicator-dot {
    height: 6rpx;
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 3rpx;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 40rpx;

    &.active {
      background-color: rgba(255, 255, 255, 0.9);
      width: 60rpx;
    }
  }
}

.card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 40rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
}

.user-info {
  color: white;
  margin-bottom: 30rpx;
}

.user-name {
  font-size: 52rpx;
  font-weight: bold;
}

.user-distance {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 28rpx;
  margin-top: 10rpx;
  opacity: 0.9;
}

.user-description {
  margin-top: 16rpx;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
}

.tag {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.action-buttons {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.action-btn-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.9);
  }

  &.primary {
    width: 140rpx;
    height: 140rpx;
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  }
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
}

.no-more-users {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
}

.no-more-text {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.no-more-desc {
  font-size: 32rpx;
  opacity: 0.7;
  display: block;
}
</style>
