<template>
  <view class="likes-container">
    <!-- z-paging 高性能列表 -->
    <z-paging
      ref="paging"
      v-model="userList"
      @query="queryList"
      class="z-paging-content"
    >
      <template #top>
        <uni-nav-bar
          :fixed="true"
          :border="false"
          :status-bar="true"
        >
          <template #left>
            <view class="nav-left">
              <view class="brand-icon" @click="goBack">
                <uni-icons type="back" size="24" color="#8B5CF6"></uni-icons>
              </view>
            </view>
          </template>
          <template #default>
              <view class="custom-tabs">
                <view
                  v-for="(tab, index) in tabs"
                  :key="index"
                  class="tab-item"
                  :class="{ active: currentTab === index }"
                  @click="onTabChange(index)"
                >
                  <text class="tab-text"
                    >{{ tab.name }} ({{ getTabCount(index) }})</text
                  >
                  <text class="tab-line"></text>
                </view>
            </view>
          </template>
        </uni-nav-bar>
      </template>
      <!-- 用户网格布局 -->
      <view class="users-grid">
        <view
          v-for="user in userList"
          :key="user.id"
          class="user-card"
          @click="goToUserDetail(user)"
        >
          <view class="card-image">
            <image :src="user.avatar" class="user-avatar" mode="aspectFill" />
            <view v-if="user.isNew" class="new-badge">NEW</view>
            <view v-if="user.isOnline" class="online-dot"></view>
          </view>
          <view class="card-info">
            <text class="user-name">{{ user.name }} ({{ user.age }})</text>
            <view class="user-distance">
              <text class="distance-text">{{ user.distance }} km away</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <template #empty>
        <view class="empty-state">
          <view class="empty-icon">💫</view>
          <text class="empty-title">{{
            currentTab === 0 ? "暂无互相喜欢" : "暂无访客记录"
          }}</text>
          <text class="empty-desc">{{
            currentTab === 0
              ? "继续匹配，寻找心动的人"
              : "当有人访问你的档案时会显示在这里"
          }}</text>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";

interface User {
  id: number;
  name: string;
  age: number;
  avatar: string;
  distance: number;
  isNew?: boolean;
  isOnline?: boolean;
}

const currentTab = ref(0);
const statusBarHeight = ref(44);
const navBarHeight = ref(88);
const userList = ref<User[]>([]);
const paging = ref<any>(null);

// 当前页码
const currentPageLikes = ref(1);
const currentPageVisitors = ref(1);

const tabs = [{ name: "Likes" }, { name: "访客" }];

// 获取标签页数量
const getTabCount = (index: number) => {
  return index === 0 ? totalLikesCount.value : totalVisitorsCount.value;
};

const totalLikesCount = ref(127);
const totalVisitorsCount = ref(48);

// 生成更多测试数据
const generateMockUsers = (
  startId: number,
  count: number,
  type: "likes" | "visitors"
): User[] => {
  const names = [
    "Monica",
    "Elizabeth",
    "Sophia",
    "Isabella",
    "Emma",
    "Lily",
    "Jessica",
    "Amanda",
    "Nicole",
    "Priya",
    "Sarah",
    "Anna",
    "Maya",
    "Grace",
    "Victoria",
    "Chloe",
    "Zoe",
    "Natalie",
    "Rachel",
    "Olivia",
    "Hannah",
    "Madison",
    "Ashley",
    "Taylor",
    "Megan",
    "Kayla",
    "Lauren",
    "Samantha",
    "Jennifer",
    "Amy",
    "Michelle",
    "Kimberly",
    "Lisa",
    "Angela",
    "Maria",
    "Helen",
    "Laura",
    "Rebecca",
    "Sharon",
    "Cynthia",
    "Donna",
    "Ruth",
    "Emily",
    "Charlotte",
    "Alice",
    "Lucy",
    "Ruby",
    "Ella",
  ];

  const avatars = [
    "https://images.unsplash.com/photo-1542345812-07426514759A?ixlib=rb-4.0.3&auto=format&fit=crop&w=880&q=80",
    "https://images.unsplash.com/photo-1604004555489-723a93d6ce74?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
    "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=764&q=80",
    "https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-4.0.3&auto=format&fit=crop&w=774&q=80",
    "https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
    "https://images.unsplash.com/photo-1488716820095-cbe80883c496?ixlib=rb-4.0.3&auto=format&fit=crop&w=686&q=80",
    "https://images.unsplash.com/photo-1520466809213-7b9a56adcd45?ixlib=rb-4.0.3&auto=format&fit=crop&w=880&q=80",
    "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=761&q=80",
    "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=688&q=80",
    "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=774&q=80",
    "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
    "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80",
    "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
    "https://images.unsplash.com/photo-1485893226505-9409047665b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=880&q=80",
  ];

  return Array.from({ length: count }, (_, index) => {
    const id = startId + index;
    const nameIndex = id % names.length;
    const avatarIndex = id % avatars.length;

    return {
      id,
      name: names[nameIndex],
      age: Math.floor(Math.random() * 15) + 18, // 18-32岁
      avatar: avatars[avatarIndex],
      distance: Math.floor(Math.random() * 50) + 1, // 1-50km
      isNew: Math.random() > 0.7, // 30%概率是新用户
      isOnline: Math.random() > 0.6, // 40%概率在线
    };
  });
};

// 模拟API请求
const mockApiRequest = (
  page: number,
  type: "likes" | "visitors"
): Promise<{ data: User[]; hasMore: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const pageSize = 20;
      const startId =
        type === "likes"
          ? (page - 1) * pageSize + 1
          : (page - 1) * pageSize + 1000;
      const mockData = generateMockUsers(startId, pageSize, type);

      // 模拟数据总量限制
      const totalCount = type === "likes" ? 127 : 48;
      const hasMore = page * pageSize < totalCount;

      resolve({
        data: hasMore
          ? mockData
          : mockData.slice(0, totalCount - (page - 1) * pageSize),
        hasMore,
      });
    }, 800); // 模拟网络延迟
  });
};

// z-paging查询方法
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const type = currentTab.value === 0 ? "likes" : "visitors";
    const result = await mockApiRequest(pageNo, type);

    // 完成查询
    paging.value?.complete(result.data);

    // 更新页码
    if (type === "likes") {
      currentPageLikes.value = pageNo;
    } else {
      currentPageVisitors.value = pageNo;
    }
  } catch (error) {
    console.error("查询失败:", error);
    paging.value?.complete(false);
  }
};

// 切换标签页
const onTabChange = async (index: number) => {
  if (currentTab.value === index) return;

  currentTab.value = index;

  // 等待DOM更新后重新加载数据
  await nextTick();
  paging.value?.reload();
};

const goBack = () => {
  uni.navigateBack();
};

const openSearch = () => {
  uni.showToast({
    title: "搜索功能",
    icon: "none",
  });
};

const openFilter = () => {
  uni.showToast({
    title: "筛选功能",
    icon: "none",
  });
};

const goToUserDetail = (user: User) => {
  uni.navigateTo({
    url: `/pages/dating/user-detail?userId=${user.id}`,
  });
};

onMounted(() => {
  // 获取系统状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 44;
  navBarHeight.value = statusBarHeight.value + 44;
});
</script>

<style scoped lang="scss">
.likes-container {
  width: 100%;
  height: 100vh;
  background-color: var(--bg-page);
}

.custom-tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.tab-item {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;

  .tab-text {
    font-size: 32rpx;
      color: var(--text-info);
    }

  &.active {
    .tab-line {
      background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
      // 增加切换过渡动画
      transition: all 1s ease-in-out;
      width: 100%;
      height: 6rpx;
      border-radius: 10rpx;
    }

    .tab-text {
      color: var(--text-base);
      font-weight: 500;
    }
  }
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.3s ease;
}

.z-paging-content {
  background: transparent !important;
  flex: 1;
  height: auto;
}

.users-grid {
  padding: 20rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.user-card {
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }
}

.card-image {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.user-avatar {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.user-card:active .user-avatar {
  transform: scale(1.05);
}

.new-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  color: white;
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  border-radius: 20rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

.online-dot {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: #10b981;
  border-radius: 50%;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.4);
}

.card-info {
  padding: 24rpx 20rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
  display: block;
  margin-bottom: 8rpx;
}

.user-distance {
  display: flex;
  align-items: center;
}

.distance-text {
  font-size: 24rpx;
  color: #9ca3af;
  font-weight: 500;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #9ca3af;
  line-height: 1.5;
}
</style>
