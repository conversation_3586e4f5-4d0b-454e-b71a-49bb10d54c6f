<template>
  <uni-popup ref="popup" :show="visible" type="bottom" @close="handleClose">
    <view class="report-modal">
      <view class="modal-header">
        <text class="modal-title">举报{{ typeText }}</text>
        <view class="close-btn" @click="handleClose">
          <uni-icons type="close" size="20" color="#9CA3AF"></uni-icons>
        </view>
      </view>

      <view class="modal-content">
        <text class="modal-desc">请选择举报原因，我们会认真处理您的举报</text>
        
        <view class="reason-list">
          <view 
            v-for="(reason, index) in reasonList" 
            :key="index"
            class="reason-item"
            :class="{ selected: selectedReason === reason.value }"
            @click="selectReason(reason.value)"
          >
            <view class="reason-content">
              <view class="reason-icon">
                <uni-icons :type="reason.icon" size="20" :color="selectedReason === reason.value ? '#8B5CF6' : '#6B7280'"></uni-icons>
              </view>
              <view class="reason-text">
                <text class="reason-title">{{ reason.title }}</text>
                <text class="reason-desc">{{ reason.desc }}</text>
              </view>
            </view>
            <view class="reason-check">
              <uni-icons 
                type="checkmarkempty" 
                size="18" 
                :color="selectedReason === reason.value ? '#8B5CF6' : 'transparent'"
              ></uni-icons>
            </view>
          </view>
        </view>

        <!-- 其他原因输入 -->
        <view v-if="selectedReason === 'other'" class="other-reason">
          <textarea
            v-model="otherReasonText"
            class="other-textarea"
            placeholder="请详细描述举报原因..."
            maxlength="200"
          />
          <text class="char-count">{{ otherReasonText.length }}/200</text>
        </view>
      </view>

      <view class="modal-footer">
        <button class="cancel-btn" @click="handleClose">取消</button>
        <button 
          class="submit-btn" 
          :class="{ disabled: !canSubmit }"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          提交举报
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface ReportReason {
  value: string;
  title: string;
  desc: string;
  icon: string;
}

const props = defineProps<{
  visible: boolean;
  type: 'post' | 'comment';
  targetId: number;
}>();

const emit = defineEmits(['close', 'submit']);

const popup = ref<any>(null);
const selectedReason = ref('');
const otherReasonText = ref('');

const typeText = computed(() => props.type === 'post' ? '动态' : '评论');

const reasonList: ReportReason[] = [
  {
    value: 'spam',
    title: '垃圾信息',
    desc: '广告、刷屏等垃圾内容',
    icon: 'trash'
  },
  {
    value: 'inappropriate',
    title: '内容不当',
    desc: '色情、暴力、血腥等不适宜内容',
    icon: 'eye-slash'
  },
  {
    value: 'harassment',
    title: '骚扰谩骂',
    desc: '人身攻击、恶意骚扰等行为',
    icon: 'chatboxes'
  },
  {
    value: 'fake',
    title: '虚假信息',
    desc: '传播谣言、虚假消息等',
    icon: 'close-circle'
  },
  {
    value: 'copyright',
    title: '侵权内容',
    desc: '盗用他人作品、侵犯版权等',
    icon: 'paperplane'
  },
  {
    value: 'other',
    title: '其他原因',
    desc: '以上都不符合，请详细说明',
    icon: 'more'
  }
];

const canSubmit = computed(() => {
  if (!selectedReason.value) return false;
  if (selectedReason.value === 'other') {
    return otherReasonText.value.trim().length > 0;
  }
  return true;
});

watch(() => props.visible, (newVal) => {
  if (newVal) {
    popup.value?.open();
  } else {
    popup.value?.close();
  }
});

const selectReason = (value: string) => {
  selectedReason.value = value;
  if (value !== 'other') {
    otherReasonText.value = '';
  }
};

const handleClose = () => {
  selectedReason.value = '';
  otherReasonText.value = '';
  emit('close');
};

const handleSubmit = () => {
  if (!canSubmit.value) return;

  const reason = selectedReason.value === 'other' 
    ? otherReasonText.value.trim()
    : reasonList.find(r => r.value === selectedReason.value)?.title || '';

  emit('submit', reason);
  handleClose();
};
</script>

<style scoped lang="scss">
.report-modal {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1F2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  
  &:active {
    background-color: #F3F4F6;
  }
}

.modal-content {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.modal-desc {
  font-size: 28rpx;
  color: #6B7280;
  line-height: 1.5;
  display: block;
  margin-bottom: 32rpx;
}

.reason-list {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.reason-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;
  
  &:active {
    background-color: #F3F4F6;
  }
  
  &.selected {
    background-color: #EEF2FF;
    border: 2rpx solid #8B5CF6;
  }
}

.reason-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.reason-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F9FAFB;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.reason-text {
  flex: 1;
}

.reason-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #1F2937;
  display: block;
  margin-bottom: 4rpx;
}

.reason-desc {
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.4;
}

.reason-check {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #E5E7EB;
  border-radius: 50%;
  margin-left: 16rpx;
}

.reason-item.selected .reason-check {
  border-color: #8B5CF6;
  background-color: #8B5CF6;
}

.other-reason {
  margin-top: 24rpx;
  padding: 24rpx;
  background-color: #F9FAFB;
  border-radius: 16rpx;
}

.other-textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  
  &::placeholder {
    color: #9CA3AF;
  }
}

.char-count {
  font-size: 24rpx;
  color: #9CA3AF;
  text-align: right;
  display: block;
  margin-top: 12rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F3F4F6;
}

.cancel-btn {
  flex: 1;
  background-color: #F3F4F6;
  color: #6B7280;
  font-size: 30rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  line-height: 1;
  margin: 0;
  border: none;
  
  &:active {
    background-color: #E5E7EB;
  }
}

.submit-btn {
  flex: 1;
  background-color: #8B5CF6;
  color: white;
  font-size: 30rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  line-height: 1;
  margin: 0;
  border: none;
  transition: all 0.2s ease;
  
  &:active:not(.disabled) {
    background-color: #7C3AED;
    transform: scale(0.98);
  }
  
  &.disabled {
    background-color: #D1D5DB;
    color: #9CA3AF;
  }
}
</style> 