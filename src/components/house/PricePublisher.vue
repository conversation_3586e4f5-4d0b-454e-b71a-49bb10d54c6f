<template>
  <view class="price-publisher">
    <view class="card">
      <view class="section-title">
        <text class="required">*</text>
        <text>价格信息</text>
        <view class="title-badge">
          <text class="badge-text">智能估价</text>
        </view>
      </view>

      <!-- 价格输入区域 -->
      <view class="price-input-section">
        <!-- 主价格输入 -->
        <tui-form-item
          :prop="priceField"
          :label="priceLabel"
          labelSize="30rpx"
          labelColor="var(--text-secondary)"
          :asterisk="true"
          asteriskColor="#ff4757"
        >
          <view class="input-with-unit">
            <tui-input
              :value="props.modelValue[priceField] || ''"
              :placeholder="`请输入${priceLabel}`"
              :type="inputType"
              :borderBottom="false"
              padding="0"
              color="var(--text-base)"
              placeholderStyle="color: var(--text-grey)"
              @input="onPriceInput"
            />
          </view>
          <template #right>
            <text class="price-unit">{{ priceUnit }}</text>
          </template>
        </tui-form-item>

        <!-- 价格区间输入（新房/商铺） -->
        <view
          v-if="type === 'newHouse' || type === 'commercial'"
          class="price-range-section"
        >
          <tui-form-item
            label="价格区间"
            labelSize="30rpx"
            labelColor="var(--text-secondary)"
          >
            <view class="price-range-inputs">
              <tui-input
                :value="props.modelValue.minPrice || ''"
                placeholder="最低价"
                :type="inputType"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="(value) => onRangeInput('minPrice', value)"
              />
              <text class="range-separator">-</text>
              <tui-input
                :value="props.modelValue.maxPrice || ''"
                placeholder="最高价"
                :type="inputType"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="(value) => onRangeInput('maxPrice', value)"
              ></tui-input>
            </view>
            <template #right>
              <text class="price-unit">{{ priceUnit }}</text>
            </template>
          </tui-form-item>
        </view>

        <!-- 付款方式选择 -->
        <tui-form-item
          v-if="showPaymentMethod"
          label="付款方式"
          labelSize="30rpx"
          labelColor="var(--text-secondary)"
          asterisk
          arrow
          asteriskColor="#ff4757"
        >
          <picker
            mode="selector"
            :range="paymentOptions"
            :range-key="'label'"
            :value="paymentMethodIndex"
            @change="onPaymentMethodChange"
          >
            <tui-input
              :value="props.modelValue.paymentMethod || ''"
              placeholder="请选择付款方式"
              :borderBottom="false"
              disabled
              padding="0"
              color="var(--text-base)"
              placeholderStyle="color: var(--text-grey)"
            />
          </picker>
        </tui-form-item>
      </view>

      <!-- 价格策略提醒 -->
      <view class="price-tips">
        <view class="tip-item">
          <text class="tip-icon">📈</text>
          <text class="tip-text">{{ getPriceTip() }}</text>
        </view>
        <view v-if="type === 'rent'" class="tip-item">
          <text class="tip-icon">🏷️</text>
          <text class="tip-text">合理定价可提升50%出租速度</text>
        </view>
      </view>

      <!-- VIP增值服务 -->
      <view class="vip-services">
        <view class="vip-header">
          <text class="vip-title">🔥 发布加速服务</text>
          <text class="vip-badge">推荐</text>
        </view>

        <view class="service-list">
          <view
            v-for="service in vipServices"
            :key="service.type"
            class="service-item"
            :class="{ selected: selectedServices.includes(service.type) }"
            @tap="toggleService(service.type)"
          >
            <view class="service-content">
              <view class="service-info">
                <view class="service-header">
                  <text class="service-icon">{{ service.icon }}</text>
                  <text class="service-name">{{ service.name }}</text>
                  <text v-if="service.popular" class="popular-badge">推荐</text>
                </view>
                <text class="service-desc">{{ service.desc }}</text>
                <text v-if="service.duration" class="service-duration"
                  >时长：{{ service.duration }}</text
                >
              </view>
              <view class="service-price">
                <text class="original-price" v-if="service.originalPrice"
                  >¥{{ service.originalPrice }}</text
                >
                <text class="current-price">¥{{ service.price }}</text>
              </view>
            </view>
            <view class="service-checkbox">
              <text
                v-if="selectedServices.includes(service.type)"
                class="i-carbon-checkmark"
              ></text>
            </view>
          </view>
        </view>

        <view class="total-price">
          <text class="total-label">总计：</text>
          <text class="total-amount">¥{{ totalServicePrice }}</text>
          <text class="save-amount" v-if="totalSaveAmount > 0"
            >（节省¥{{ totalSaveAmount }}）</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { PAYMENT_METHOD_OPTIONS, NEW_HOUSE_PAYMENT_OPTIONS } from "@/constants";

interface Props {
  type: "rent" | "newHouse" | "secondHand" | "commercial";
  modelValue: Record<string, any>;
  showPaymentMethod?: boolean;
}

interface Emits {
  (e: "update:modelValue", value: Record<string, any>): void;
  (e: "serviceChange", services: string[], totalPrice: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  showPaymentMethod: true,
});

const emit = defineEmits<Emits>();

const paymentMethodIndex = ref(0);
const selectedServices = ref<string[]>([]);

// 监听props变化，更新本地数据
watch(
  () => props.modelValue,
  (newValue) => {
    // 不需要额外处理，直接使用props.modelValue
  },
  { immediate: true }
);

// 价格配置
const priceConfig = computed(() => {
  switch (props.type) {
    case "rent":
      return {
        field: "monthlyRent",
        label: "月租金",
        unit: "元/月",
        inputType: "number",
      };
    case "newHouse":
      return {
        field: "averagePrice",
        label: "均价",
        unit: "元/㎡",
        inputType: "number",
      };
    case "secondHand":
      return {
        field: "totalPrice",
        label: "总价",
        unit: "万元",
        inputType: "digit",
      };
    case "commercial":
      return {
        field: "price",
        label: "价格",
        unit: "元/㎡·天",
        inputType: "digit",
      };
    default:
      return {
        field: "price",
        label: "价格",
        unit: "元",
        inputType: "number",
      };
  }
});

const priceField = computed(() => priceConfig.value.field);
const priceLabel = computed(() => priceConfig.value.label);
const priceUnit = computed(() => priceConfig.value.unit);
const inputType = computed(() => priceConfig.value.inputType);

// 付款方式选项
const paymentOptions = computed(() => {
  return props.type === "newHouse" || props.type === "commercial"
    ? NEW_HOUSE_PAYMENT_OPTIONS
    : PAYMENT_METHOD_OPTIONS;
});

// 增值服务配置 - 简化且实用的服务，适合三四线城市
const vipServices = computed(() => {
  // 基础服务配置
  const baseServices = [
    {
      type: "top_3days",
      name: "3天置顶",
      desc: "房源排序靠前3天，提升曝光率",
      price: getServicePrice("top_3days"),
      originalPrice: getOriginalPrice("top_3days"),
      icon: "🔝",
      duration: "3天",
      popular: true, // 推荐标识
    },
    {
      type: "top_7days",
      name: "7天置顶",
      desc: "房源排序靠前7天，更多曝光机会",
      price: getServicePrice("top_7days"),
      originalPrice: getOriginalPrice("top_7days"),
      icon: "🔝",
      duration: "7天",
      popular: true, // 推荐标识
    },
    {
      type: "highlight",
      name: "高亮显示",
      desc: "房源高亮展示，醒目易发现",
      price: getServicePrice("highlight"),
      originalPrice: getOriginalPrice("highlight"),
      icon: "✨",
      duration: "发布期间",
      popular: false, // 推荐标识
    },
  ];

  // 根据房源类型添加特定服务
  const typeSpecificServices = getTypeSpecificServices();
  return [...baseServices, ...typeSpecificServices];
});

// 根据房源类型获取服务价格
const getServicePrice = (serviceType: string) => {
  const pricing = {
    rent: {
      top_3days: 15,
      top_7days: 25,
      highlight: 10,
      urgent: 8,
      contact_protection: 5,
    },
    newHouse: {
      top_3days: 39,
      top_7days: 69,
      highlight: 29,
      phone_display: 19,
      wechat_promotion: 49,
    },
    secondHand: {
      top_3days: 29,
      top_7days: 49,
      highlight: 19,
      price_analysis: 15,
      urgent: 12,
    },
    commercial: {
      top_3days: 59,
      top_7days: 99,
      highlight: 39,
      business_hours: 29,
      contact_priority: 19,
    },
  };

  return pricing[props.type]?.[serviceType] || 0;
};

const getOriginalPrice = (serviceType: string) => {
  const originalPricing = {
    rent: {
      top_3days: 30,
      top_7days: 50,
      highlight: 20,
      urgent: 16,
      contact_protection: 10,
    },
    newHouse: {
      top_3days: 78,
      top_7days: 138,
      highlight: 58,
      phone_display: 38,
      wechat_promotion: 98,
    },
    secondHand: {
      top_3days: 58,
      top_7days: 98,
      highlight: 38,
      price_analysis: 30,
      urgent: 24,
    },
    commercial: {
      top_3days: 118,
      top_7days: 198,
      highlight: 78,
      business_hours: 58,
      contact_priority: 38,
    },
  };

  return originalPricing[props.type]?.[serviceType] || 0;
};

// 根据房源类型获取特定服务
const getTypeSpecificServices = () => {
  switch (props.type) {
    case "rent":
      return [
        {
          type: "urgent",
          name: "急租标识",
          desc: "显示急租标签，快速找到租客",
          price: getServicePrice("urgent"),
          originalPrice: getOriginalPrice("urgent"),
          icon: "⚡",
          duration: "发布期间",
        },
        {
          type: "contact_protection",
          name: "联系方式保护",
          desc: "防骚扰，只展示认证用户联系方式",
          price: getServicePrice("contact_protection"),
          originalPrice: getOriginalPrice("contact_protection"),
          icon: "🛡️",
          duration: "发布期间",
        },
      ];
    case "newHouse":
      return [
        {
          type: "phone_display",
          name: "售楼热线展示",
          desc: "突出显示项目销售电话",
          price: getServicePrice("phone_display"),
          originalPrice: getOriginalPrice("phone_display"),
          icon: "📞",
          duration: "发布期间",
        },
        {
          type: "wechat_promotion",
          name: "微信群推广",
          desc: "发布到相关楼盘微信群",
          price: getServicePrice("wechat_promotion"),
          originalPrice: getOriginalPrice("wechat_promotion"),
          icon: "💬",
          duration: "一次性",
        },
      ];
    case "secondHand":
      return [
        {
          type: "price_analysis",
          name: "价格分析",
          desc: "提供同小区成交价格参考",
          price: getServicePrice("price_analysis"),
          originalPrice: getOriginalPrice("price_analysis"),
          icon: "📊",
          duration: "一次性",
        },
        {
          type: "urgent",
          name: "急售标识",
          desc: "显示急售标签，快速成交",
          price: getServicePrice("urgent"),
          originalPrice: getOriginalPrice("urgent"),
          icon: "⚡",
          duration: "发布期间",
        },
      ];
    case "commercial":
      return [
        {
          type: "business_hours",
          name: "营业时间展示",
          desc: "突出显示商铺营业时间",
          price: getServicePrice("business_hours"),
          originalPrice: getOriginalPrice("business_hours"),
          icon: "🕐",
          duration: "发布期间",
        },
        {
          type: "contact_priority",
          name: "优先联系",
          desc: "联系方式排序置顶显示",
          price: getServicePrice("contact_priority"),
          originalPrice: getOriginalPrice("contact_priority"),
          icon: "📱",
          duration: "发布期间",
        },
      ];
    default:
      return [];
  }
};

const totalServicePrice = computed(() => {
  return selectedServices.value.reduce((total, serviceType) => {
    const service = vipServices.value.find((s) => s.type === serviceType);
    return total + (service?.price || 0);
  }, 0);
});

const totalSaveAmount = computed(() => {
  return selectedServices.value.reduce((total, serviceType) => {
    const service = vipServices.value.find((s) => s.type === serviceType);
    return total + ((service?.originalPrice || 0) - (service?.price || 0));
  }, 0);
});

// 监听服务选择变化
watch(
  [selectedServices, totalServicePrice],
  ([services, price]) => {
    emit("serviceChange", services, price);
  },
  { deep: true }
);

const onPriceInput = (value: string) => {
  // 价格输入验证
  let processedValue = value;
  if (props.type === "secondHand" || props.type === "commercial") {
    // 支持小数点
    processedValue = value;
  } else {
    // 只允许整数
    processedValue = value.replace(/[^\d]/g, "");
  }

  // 更新父组件数据
  const newData = { ...props.modelValue };
  newData[priceField.value] = processedValue;
  emit("update:modelValue", newData);
};

const onRangeInput = (field: string, value: string) => {
  // 价格区间输入处理
  const newData = { ...props.modelValue };
  newData[field] = value;
  emit("update:modelValue", newData);
};

const onPaymentMethodChange = (e: any) => {
  const index = e.detail.value;
  paymentMethodIndex.value = index;
  const newData = { ...props.modelValue };
  newData.paymentMethod = paymentOptions.value[index].label;
  emit("update:modelValue", newData);
};

const toggleService = (serviceType: string) => {
  const index = selectedServices.value.indexOf(serviceType);
  if (index > -1) {
    selectedServices.value.splice(index, 1);
  } else {
    selectedServices.value.push(serviceType);
  }
};

const getPriceTip = () => {
  switch (props.type) {
    case "rent":
      return "租金定价建议比周边低5-10%，更容易快速成交";
    case "newHouse":
      return "合理均价有助于吸引更多客户咨询";
    case "secondHand":
      return "房源定价建议参考近期成交价格";
    case "commercial":
      return "商业地产定价需考虑地段和客流量";
    default:
      return "合理定价有助于快速成交";
  }
};
</script>

<style lang="scss" scoped>
.price-publisher {
  .card {
    background: linear-gradient(135deg, #ffffff, #fafbfc);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid #f0f0f0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;

  .required {
    color: var(--text-red);
    margin-right: 8rpx;
    font-weight: 600;
  }

  .title-badge {
    margin-left: 16rpx;
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    padding: 4rpx 12rpx;
    border-radius: 12rpx;

    .badge-text {
      font-size: 20rpx;
      color: white;
      font-weight: 500;
    }
  }
}

.price-input-section {
  margin-bottom: 32rpx;
}

.input-with-unit {
  display: flex;
  align-items: center;
  width: 100%;
}

.price-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-left: 16rpx;
  white-space: nowrap;
}

.price-range-section {
  margin-top: 16rpx;
}

.price-range-inputs {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .range-separator {
    color: var(--text-secondary);
    font-size: 28rpx;
    font-weight: 500;
  }
}

.price-tips {
  background: #fff8f0;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #ffe7d1;

  .tip-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .tip-icon {
      margin-right: 8rpx;
      font-size: 28rpx;
    }

    .tip-text {
      font-size: 26rpx;
      color: var(--text-secondary);
      line-height: 1.4;
      flex: 1;
    }
  }
}

.vip-services {
  background: linear-gradient(135deg, #fff5f5, #ffeef0);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #ffe0e6;
}

.vip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;

  .vip-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-base);
  }

  .vip-badge {
    background: linear-gradient(135deg, #ff4757, #ff6b7a);
    color: white;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: 500;
  }
}

.service-list {
  .service-item {
    display: flex;
    align-items: center;
    padding: 16rpx;
    margin-bottom: 12rpx;
    background: white;
    border-radius: 12rpx;
    border: 2rpx solid transparent;
    transition: all 0.2s ease;

    &.selected {
      border-color: var(--primary);
      background: rgba(79, 172, 254, 0.05);
    }

    &:active {
      transform: scale(0.98);
    }
  }
}

.service-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-info {
  flex: 1;
}

.service-header {
  display: flex;
  align-items: center;
  margin-bottom: 4rpx;
}

.service-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popular-badge {
  font-size: 20rpx;
  background: linear-gradient(135deg, #ff4757, #ff6b7a);
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  margin-left: 8rpx;
  font-weight: 500;
}

.service-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.3;
  margin-bottom: 4rpx;
}

.service-duration {
  font-size: 22rpx;
  color: var(--text-info);
  font-weight: 500;
}

.service-price {
  text-align: right;
}

.original-price {
  font-size: 22rpx;
  color: var(--text-info);
  text-decoration: line-through;
  display: block;
  margin-bottom: 4rpx;
}

.current-price {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary);
}

.service-checkbox {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid var(--text-grey);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  transition: all 0.2s ease;

  .service-item.selected & {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
  }
}

.total-price {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;

  .total-label {
    font-size: 28rpx;
    color: var(--text-secondary);
  }

  .total-amount {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--primary);
    margin: 0 8rpx;
  }

  .save-amount {
    font-size: 24rpx;
    color: #10b981;
  }
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-info {
  .service-name {
    font-size: 30rpx;
    font-weight: 500;
    color: var(--text-base);
    display: block;
    margin-bottom: 4rpx;
  }

  .service-desc {
    font-size: 24rpx;
    color: var(--text-info);
  }
}

.service-price {
  text-align: right;

  .original-price {
    font-size: 24rpx;
    color: var(--text-info);
    text-decoration: line-through;
    display: block;
    margin-bottom: 2rpx;
  }

  .current-price {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--text-red);
  }
}

.service-checkbox {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid var(--text-grey);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;

  .service-item.selected & {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
  }
}

.total-price {
  text-align: right;
  padding-top: 16rpx;
  border-top: 1rpx solid #ffe0e6;
  margin-top: 16rpx;

  .total-label {
    font-size: 30rpx;
    color: var(--text-secondary);
  }

  .total-amount {
    font-size: 36rpx;
    font-weight: 600;
    color: var(--text-red);
    margin-left: 8rpx;
  }

  .save-amount {
    font-size: 24rpx;
    color: var(--success);
    margin-left: 8rpx;
  }
}
</style>
